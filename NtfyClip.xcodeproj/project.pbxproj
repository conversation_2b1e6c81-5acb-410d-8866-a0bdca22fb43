// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1000001000000000000001 /* NtfyClipApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000002000000000000001 /* NtfyClipApp.swift */; };
		A1000003000000000000001 /* MainWindowView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000004000000000000001 /* MainWindowView.swift */; };
		A1000005000000000000001 /* MenuBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000006000000000000001 /* MenuBarView.swift */; };
		A1000007000000000000001 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000008000000000000001 /* SettingsView.swift */; };
		A1000009000000000000001 /* AppViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000010000000000000001 /* AppViewModel.swift */; };
		A1000011000000000000001 /* ClipboardSender.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000012000000000000001 /* ClipboardSender.swift */; };
		A1000013000000000000001 /* ClipboardReceiver.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000014000000000000001 /* ClipboardReceiver.swift */; };
		A1000015000000000000001 /* NtfyService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000016000000000000001 /* NtfyService.swift */; };
		A1000017000000000000001 /* ClipboardManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000018000000000000001 /* ClipboardManager.swift */; };
		A1000019000000000000001 /* ConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000020000000000000001 /* ConfigurationManager.swift */; };
		A1000021000000000000001 /* AppModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000022000000000000001 /* AppModels.swift */; };
		A1000023000000000000001 /* ContentIdentifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000024000000000000001 /* ContentIdentifier.swift */; };
		A1000047000000000000001 /* StatusDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000048000000000000001 /* StatusDetailView.swift */; };
		A1000049000000000000001 /* SettingsDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000050000000000000001 /* SettingsDetailView.swift */; };
		A1000051000000000000001 /* LogsDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000052000000000000001 /* LogsDetailView.swift */; };
		A1000053000000000000001 /* LogManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1000054000000000000001 /* LogManager.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1000025000000000000001 /* NtfyClip.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = NtfyClip.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1000002000000000000001 /* NtfyClipApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NtfyClipApp.swift; sourceTree = "<group>"; };
		A1000004000000000000001 /* MainWindowView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainWindowView.swift; sourceTree = "<group>"; };
		A1000006000000000000001 /* MenuBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MenuBarView.swift; sourceTree = "<group>"; };
		A1000008000000000000001 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A1000010000000000000001 /* AppViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppViewModel.swift; sourceTree = "<group>"; };
		A1000012000000000000001 /* ClipboardSender.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClipboardSender.swift; sourceTree = "<group>"; };
		A1000014000000000000001 /* ClipboardReceiver.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClipboardReceiver.swift; sourceTree = "<group>"; };
		A1000016000000000000001 /* NtfyService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NtfyService.swift; sourceTree = "<group>"; };
		A1000018000000000000001 /* ClipboardManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClipboardManager.swift; sourceTree = "<group>"; };
		A1000020000000000000001 /* ConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationManager.swift; sourceTree = "<group>"; };
		A1000022000000000000001 /* AppModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppModels.swift; sourceTree = "<group>"; };
		A1000024000000000000001 /* ContentIdentifier.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentIdentifier.swift; sourceTree = "<group>"; };
		A1000026000000000000001 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1000048000000000000001 /* StatusDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StatusDetailView.swift; sourceTree = "<group>"; };
		A1000050000000000000001 /* SettingsDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsDetailView.swift; sourceTree = "<group>"; };
		A1000052000000000000001 /* LogsDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LogsDetailView.swift; sourceTree = "<group>"; };
		A1000054000000000000001 /* LogManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LogManager.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1000027000000000000001 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1000028000000000000001 = {
			isa = PBXGroup;
			children = (
				A1000029000000000000001 /* NtfyClip */,
				A1000030000000000000001 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1000029000000000000001 /* NtfyClip */ = {
			isa = PBXGroup;
			children = (
				A1000002000000000000001 /* NtfyClipApp.swift */,
				A1000031000000000000001 /* Views */,
				A1000032000000000000001 /* ViewModels */,
				A1000033000000000000001 /* Actors */,
				A1000034000000000000001 /* Services */,
				A1000035000000000000001 /* Models */,
				A1000036000000000000001 /* Utils */,
				A1000026000000000000001 /* Info.plist */,
			);
			path = NtfyClip;
			sourceTree = "<group>";
		};
		A1000030000000000000001 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1000025000000000000001 /* NtfyClip.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1000031000000000000001 /* Views */ = {
			isa = PBXGroup;
			children = (
				A1000004000000000000001 /* MainWindowView.swift */,
				A1000006000000000000001 /* MenuBarView.swift */,
				A1000008000000000000001 /* SettingsView.swift */,
				A1000048000000000000001 /* StatusDetailView.swift */,
				A1000050000000000000001 /* SettingsDetailView.swift */,
				A1000052000000000000001 /* LogsDetailView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1000032000000000000001 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				A1000010000000000000001 /* AppViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		A1000033000000000000001 /* Actors */ = {
			isa = PBXGroup;
			children = (
				A1000012000000000000001 /* ClipboardSender.swift */,
				A1000014000000000000001 /* ClipboardReceiver.swift */,
			);
			path = Actors;
			sourceTree = "<group>";
		};
		A1000034000000000000001 /* Services */ = {
			isa = PBXGroup;
			children = (
				A1000016000000000000001 /* NtfyService.swift */,
				A1000018000000000000001 /* ClipboardManager.swift */,
				A1000020000000000000001 /* ConfigurationManager.swift */,
				A1000054000000000000001 /* LogManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1000035000000000000001 /* Models */ = {
			isa = PBXGroup;
			children = (
				A1000022000000000000001 /* AppModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1000036000000000000001 /* Utils */ = {
			isa = PBXGroup;
			children = (
				A1000024000000000000001 /* ContentIdentifier.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1000037000000000000001 /* NtfyClip */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1000038000000000000001 /* Build configuration list for PBXNativeTarget "NtfyClip" */;
			buildPhases = (
				A1000039000000000000001 /* Sources */,
				A1000027000000000000001 /* Frameworks */,
				A1000040000000000000001 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = NtfyClip;
			productName = NtfyClip;
			productReference = A1000025000000000000001 /* NtfyClip.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1000041000000000000001 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1000037000000000000001 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1000042000000000000001 /* Build configuration list for PBXProject "NtfyClip" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1000028000000000000001;
			productRefGroup = A1000030000000000000001 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1000037000000000000001 /* NtfyClip */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1000040000000000000001 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1000039000000000000001 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1000001000000000000001 /* NtfyClipApp.swift in Sources */,
				A1000003000000000000001 /* MainWindowView.swift in Sources */,
				A1000005000000000000001 /* MenuBarView.swift in Sources */,
				A1000007000000000000001 /* SettingsView.swift in Sources */,
				A1000009000000000000001 /* AppViewModel.swift in Sources */,
				A1000011000000000000001 /* ClipboardSender.swift in Sources */,
				A1000013000000000000001 /* ClipboardReceiver.swift in Sources */,
				A1000015000000000000001 /* NtfyService.swift in Sources */,
				A1000017000000000000001 /* ClipboardManager.swift in Sources */,
				A1000019000000000000001 /* ConfigurationManager.swift in Sources */,
				A1000021000000000000001 /* AppModels.swift in Sources */,
				A1000023000000000000001 /* ContentIdentifier.swift in Sources */,
				A1000047000000000000001 /* StatusDetailView.swift in Sources */,
				A1000049000000000000001 /* SettingsDetailView.swift in Sources */,
				A1000051000000000000001 /* LogsDetailView.swift in Sources */,
				A1000053000000000000001 /* LogManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1000043000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1000044000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		A1000045000000000000001 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NtfyClip/NtfyClip.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NtfyClip/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ntfyclip.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A1000046000000000000001 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = NtfyClip/NtfyClip.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = NtfyClip/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.ntfyclip.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1000038000000000000001 /* Build configuration list for PBXNativeTarget "NtfyClip" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000045000000000000001 /* Debug */,
				A1000046000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1000042000000000000001 /* Build configuration list for PBXProject "NtfyClip" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1000043000000000000001 /* Debug */,
				A1000044000000000000001 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1000041000000000000001 /* Project object */;
}
