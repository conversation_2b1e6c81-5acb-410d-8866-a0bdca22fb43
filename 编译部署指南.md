# NtfyClip 编译、启动和打包指南 🛠️

**完整的 macOS 应用程序构建和部署教程**

本指南将详细介绍如何编译、启动和打包 NtfyClip 应用程序，包括开发环境配置、构建过程、调试技巧和发布准备。

## 📋 系统要求

### 必需环境
- **macOS**: 13.0 (Ventura) 或更高版本
- **Xcode**: 15.0 或更高版本
- **Swift**: 5.9+ 工具链
- **内存**: 至少 8GB RAM（推荐 16GB）
- **存储**: 至少 5GB 可用空间

### 开发工具
- **Xcode Command Line Tools**: 用于命令行构建
- **Git**: 版本控制和源码管理
- **终端**: 命令行操作

## 🚀 环境配置

### 1. 安装 Xcode
```bash
# 从 App Store 安装 Xcode，或者使用命令行
xcode-select --install

# 验证安装
xcode-select -p
# 应该显示: /Applications/Xcode.app/Contents/Developer
```

### 2. 验证 Swift 版本
```bash
swift --version
# 应该显示 Swift 5.9 或更高版本
```

### 3. 配置开发者账户（可选）
如果需要在真实设备上运行或发布到 App Store：
- 在 Xcode 中登录 Apple Developer 账户
- 配置代码签名证书
- 设置 Provisioning Profile

## 📥 获取源码

### 方法一：Git 克隆
```bash
# 克隆仓库
git clone https://github.com/your-username/NtfyClip.git

# 进入项目目录
cd NtfyClip

# 查看项目结构
ls -la
```

### 方法二：下载 ZIP
1. 访问 GitHub 仓库页面
2. 点击 "Code" → "Download ZIP"
3. 解压到本地目录
4. 在终端中导航到项目目录

## 🔨 编译项目

### 方法一：使用 Xcode（推荐）

#### 1. 打开项目
```bash
# 在项目目录中执行
open NtfyClip.xcodeproj
```

#### 2. 配置构建设置
- **选择 Scheme**: 确保选择了 "NtfyClip" scheme
- **选择目标**: 选择 "My Mac" 或具体的 Mac 设备
- **构建配置**: 
  - Debug: 用于开发和调试
  - Release: 用于发布和性能测试

#### 3. 构建项目
```
快捷键: ⌘+B (Build)
或者: Product → Build
```

#### 4. 运行项目
```
快捷键: ⌘+R (Run)
或者: Product → Run
```

### 方法二：命令行构建

#### 1. Debug 构建
```bash
# 进入项目目录
cd /path/to/NtfyClip

# 构建 Debug 版本
xcodebuild -project NtfyClip.xcodeproj \
           -scheme NtfyClip \
           -configuration Debug \
           build

# 查看构建结果
ls -la ~/Library/Developer/Xcode/DerivedData/NtfyClip-*/Build/Products/Debug/
```

#### 2. Release 构建
```bash
# 构建 Release 版本
xcodebuild -project NtfyClip.xcodeproj \
           -scheme NtfyClip \
           -configuration Release \
           build

# 查看构建结果
ls -la ~/Library/Developer/Xcode/DerivedData/NtfyClip-*/Build/Products/Release/
```

#### 3. 清理构建
```bash
# 清理之前的构建产物
xcodebuild -project NtfyClip.xcodeproj \
           -scheme NtfyClip \
           clean

# 或者删除 DerivedData
rm -rf ~/Library/Developer/Xcode/DerivedData/NtfyClip-*
```

## 🚀 启动应用程序

### 方法一：从 Xcode 启动
1. 在 Xcode 中按 `⌘+R`
2. 应用程序将自动构建并启动
3. 主窗口会显示 Liquid Glass 界面
4. 菜单栏会出现 NtfyClip 图标

### 方法二：从命令行启动

#### 启动 Debug 版本
```bash
# 找到构建的应用程序
DERIVED_DATA_PATH=$(xcodebuild -showBuildSettings -project NtfyClip.xcodeproj -scheme NtfyClip | grep "BUILD_DIR" | head -1 | sed 's/.*= //')

# 启动应用程序
open "$DERIVED_DATA_PATH/Debug/NtfyClip.app"
```

#### 启动 Release 版本
```bash
# 启动 Release 版本
open "$DERIVED_DATA_PATH/Release/NtfyClip.app"
```

#### 直接运行可执行文件
```bash
# 直接运行（用于调试）
"$DERIVED_DATA_PATH/Debug/NtfyClip.app/Contents/MacOS/NtfyClip"
```

### 方法三：安装到 Applications 文件夹
```bash
# 复制到 Applications 文件夹
cp -R "$DERIVED_DATA_PATH/Release/NtfyClip.app" /Applications/

# 从 Applications 启动
open /Applications/NtfyClip.app
```

## 📦 打包和分发

### 1. 创建 Archive

#### 使用 Xcode
1. 选择 "Any Mac" 作为目标
2. Product → Archive
3. 等待 Archive 完成
4. Organizer 窗口会自动打开

#### 使用命令行
```bash
# 创建 Archive
xcodebuild -project NtfyClip.xcodeproj \
           -scheme NtfyClip \
           -configuration Release \
           -archivePath ./NtfyClip.xcarchive \
           archive
```

### 2. 导出应用程序

#### 开发分发（Development）
```bash
# 导出用于开发测试的版本
xcodebuild -exportArchive \
           -archivePath ./NtfyClip.xcarchive \
           -exportPath ./Export \
           -exportOptionsPlist ExportOptions.plist
```

#### App Store 分发
```bash
# 导出用于 App Store 的版本
xcodebuild -exportArchive \
           -archivePath ./NtfyClip.xcarchive \
           -exportPath ./AppStore \
           -exportOptionsPlist AppStoreExportOptions.plist
```

### 3. 创建 DMG 安装包

#### 使用 hdiutil 创建 DMG
```bash
# 创建临时目录
mkdir -p ./DMG/NtfyClip

# 复制应用程序
cp -R ./Export/NtfyClip.app ./DMG/NtfyClip/

# 创建 Applications 链接
ln -s /Applications ./DMG/NtfyClip/Applications

# 创建 DMG
hdiutil create -volname "NtfyClip" \
               -srcfolder ./DMG/NtfyClip \
               -ov -format UDZO \
               NtfyClip.dmg
```

## 🔧 调试和故障排除

### 常见构建错误

#### 1. 代码签名错误
```bash
# 错误信息: Code signing error
# 解决方案: 配置开发者账户和证书
```

**解决步骤:**
1. 打开 Xcode → Preferences → Accounts
2. 添加 Apple ID 账户
3. 在项目设置中选择正确的 Team
4. 确保 Bundle Identifier 唯一

#### 2. 依赖项错误
```bash
# 错误信息: Missing dependencies
# 解决方案: 清理并重新构建
```

**解决步骤:**
```bash
# 清理项目
xcodebuild clean

# 删除 DerivedData
rm -rf ~/Library/Developer/Xcode/DerivedData/NtfyClip-*

# 重新构建
xcodebuild build
```

#### 3. Swift 版本不兼容
```bash
# 错误信息: Swift version incompatible
# 解决方案: 更新 Xcode 和 Swift 工具链
```

### 调试技巧

#### 1. 查看详细构建日志
```bash
# 显示详细构建信息
xcodebuild -project NtfyClip.xcodeproj \
           -scheme NtfyClip \
           -configuration Debug \
           build \
           -verbose
```

#### 2. 检查应用程序包结构
```bash
# 查看应用程序包内容
ls -la NtfyClip.app/Contents/
ls -la NtfyClip.app/Contents/MacOS/
ls -la NtfyClip.app/Contents/Resources/
```

#### 3. 验证代码签名
```bash
# 检查代码签名状态
codesign -dv --verbose=4 NtfyClip.app

# 验证签名
codesign --verify --verbose NtfyClip.app
```

#### 4. 运行时调试
```bash
# 使用 lldb 调试
lldb NtfyClip.app/Contents/MacOS/NtfyClip

# 在 lldb 中运行
(lldb) run

# 设置断点
(lldb) breakpoint set --name main
```

## 🎯 性能优化

### 构建优化
```bash
# 使用并行构建
xcodebuild -project NtfyClip.xcodeproj \
           -scheme NtfyClip \
           -configuration Release \
           -jobs $(sysctl -n hw.ncpu) \
           build
```

### 应用程序大小优化
1. **启用 App Thinning**: 在 Archive 时自动优化
2. **移除未使用资源**: 清理无用的图片和文件
3. **代码优化**: 使用 Release 配置构建

### 启动时间优化
1. **减少启动时依赖**: 延迟加载非关键组件
2. **优化资源加载**: 使用异步加载
3. **预编译资源**: 使用 Asset Catalog

## 📋 发布检查清单

### 构建前检查
- [ ] 更新版本号和构建号
- [ ] 检查代码签名配置
- [ ] 验证所有功能正常工作
- [ ] 运行单元测试和 UI 测试
- [ ] 检查内存泄漏和性能问题

### 打包前检查
- [ ] 使用 Release 配置构建
- [ ] 验证 Liquid Glass 效果正常
- [ ] 测试在不同 macOS 版本上的兼容性
- [ ] 检查应用程序图标和资源
- [ ] 验证菜单栏集成功能

### 分发前检查
- [ ] 测试 DMG 安装包
- [ ] 验证应用程序在全新系统上的运行
- [ ] 检查网络连接和 ntfy 集成
- [ ] 测试所有用户界面功能
- [ ] 验证文档和帮助信息

## 🎉 完成！

恭喜！您已经成功学会了如何编译、启动和打包 NtfyClip 应用程序。现在您可以：

1. **开发**: 修改代码并测试新功能
2. **调试**: 使用 Xcode 调试工具解决问题
3. **分发**: 创建安装包分享给其他用户
4. **发布**: 准备提交到 App Store

如果遇到问题，请参考故障排除部分或查看项目的 [README.md](README.md) 和 [QUICK_START.md](QUICK_START.md) 文档。

---

**祝您使用愉快！享受美丽的 Liquid Glass 界面带来的现代化剪贴板同步体验！** ✨
