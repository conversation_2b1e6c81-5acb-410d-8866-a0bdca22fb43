# NtfyClip Quick Start Guide 🚀

**Get up and running with <PERSON>tfy<PERSON><PERSON> in under 5 minutes**

This guide will help you quickly set up and start using NtfyClip's beautiful Liquid Glass interface for seamless clipboard synchronization across your devices.

## 📋 Prerequisites

Before you begin, ensure you have:

- **macOS 13.0 (Ventura)** or later
- **Xcode 15.0+** (for building from source)
- **Internet connection** for ntfy server communication
- **ntfy server access** (public ntfy.sh or private server)

## ⚡ Installation Methods

### Method 1: Build with Xcode (Recommended)

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-username/NtfyClip.git
   cd NtfyClip
   ```

2. **Open in Xcode**
   ```bash
   open NtfyClip.xcodeproj
   ```

3. **Build and Run**
   - Select the `NtfyClip` scheme
   - Press `⌘+R` or click the Run button
   - The app will build and launch automatically

### Method 2: Command Line Build

1. **Clone and Navigate**
   ```bash
   git clone https://github.com/your-username/NtfyClip.git
   cd NtfyClip
   ```

2. **Build the Project**
   ```bash
   xcodebuild -project NtfyClip.xcodeproj -scheme NtfyClip -configuration Debug build
   ```

3. **Launch the App**
   ```bash
   open /Users/<USER>/Library/Developer/Xcode/DerivedData/NtfyClip-*/Build/Products/Debug/NtfyClip.app
   ```

## 🎯 First Launch Setup

### 1. Welcome to NtfyClip
When you first launch NtfyClip:
- The main window opens with the beautiful Liquid Glass interface
- You'll see the enhanced sidebar with Status, Settings, and Logs
- The app icon appears in your menu bar for quick access

### 2. Initial Configuration
Navigate to the **Settings** section in the sidebar:

#### Basic Configuration
1. **Server URL**: Enter your ntfy server
   - Public server: `https://ntfy.sh`
   - Private server: `https://your-server.com`

2. **Topic Name**: Choose a unique topic
   - Example: `my-clipboard-sync-2024`
   - Make it unique to avoid conflicts with other users

3. **Save Configuration**: Click the glass-styled "Done" button

#### Advanced Configuration (Optional)
- **Authentication**: Enable if using a private server
  - Username: Your ntfy server username
  - Password: Your ntfy server password
- **Dual Configuration**: Set different topics for send/receive if needed

### 3. Start Syncing
1. Navigate to the **Status** section in the sidebar
2. Click the prominent "Start Sync" button
3. Watch the beautiful glass-styled counters come to life

## 🎨 Interface Overview

### Enhanced Sidebar (Liquid Glass Design)
- **Wider Layout**: Optimized 280-380px width for better usability
- **Improved Spacing**: Enhanced touch targets and visual breathing room
- **Glass Effects**: Subtle transparency and depth throughout
- **Vibrant Feedback**: Smooth hover animations and selection states

### Status Dashboard
- **Live Counters**: Real-time sent/received notification tracking
- **Glass Cards**: Beautiful containers with depth and transparency
- **Activity Monitoring**: Visual indicators for sync status
- **Statistics Display**: Comprehensive sync metrics

### Settings Interface
- **Glass Cards**: Each settings section in elegant glass containers
- **Enhanced Forms**: Improved touch targets and spacing
- **Visual Validation**: Real-time configuration status feedback
- **Modern Controls**: Liquid Glass styled buttons and inputs

## 🔄 Testing Your Setup

### Test Sending (Local → Remote)
1. **Copy text** to your clipboard (⌘+C)
2. **Watch the counter** in the Status dashboard increment
3. **Check your ntfy topic** via web interface or another device
4. **Verify** the text appears on the remote end

### Test Receiving (Remote → Local)
1. **Send a message** to your ntfy topic from another device or web interface
2. **Watch the received counter** increment in the Status dashboard
3. **Paste** (⌘+V) to verify the text is now in your local clipboard

## 🎯 Menu Bar Features

The NtfyClip menu bar icon provides quick access to:
- **Start/Stop Sync**: Toggle synchronization without opening the main window
- **Open Main Window**: Access the full Liquid Glass interface
- **Settings**: Quick access to configuration
- **Quit**: Clean application shutdown

## 🔧 Troubleshooting

### Common Issues and Solutions

#### App Won't Launch
- **Check macOS Version**: Ensure you're running macOS 13.0+
- **Verify Xcode**: Make sure Xcode 15.0+ is installed
- **Clean Build**: Try cleaning and rebuilding the project

#### Connection Problems
- **Server URL**: Verify the ntfy server URL is correct and accessible
- **Network**: Check your internet connection
- **Firewall**: Ensure your firewall allows the connection
- **Authentication**: Verify credentials if using a private server

#### Sync Not Working
- **Topic Names**: Ensure topic names match across devices
- **Permissions**: Check if the ntfy server requires authentication
- **Logs**: Check the Logs section for detailed error messages
- **Restart**: Try stopping and starting sync again

#### Visual Issues
- **Window Size**: Ensure minimum window size (1000x700) for optimal layout
- **Display**: Check if running on external displays affects glass effects
- **Performance**: Verify system meets minimum requirements

## 🎨 Customization Tips

### Optimizing the Interface
- **Sidebar Width**: The sidebar automatically adjusts between 280-380px
- **Window Size**: Minimum 1000x700 for best Liquid Glass experience
- **Theme**: The app automatically adapts to your system's light/dark mode

### Performance Optimization
- **Background Operation**: The app runs efficiently in the background
- **Memory Usage**: Typically uses less than 50MB of RAM
- **CPU Impact**: Minimal CPU usage during normal operation

## 📊 Understanding the Dashboard

### Counter Meanings
- **Sent**: Number of clipboard items sent to ntfy since app launch
- **Received**: Number of items received from ntfy since app launch
- **Total**: Combined count of all sync operations

### Status Indicators
- **Green**: Connected and syncing normally
- **Yellow**: Connected but with warnings
- **Red**: Connection issues or errors
- **Gray**: Sync stopped or not configured

## 🚀 Next Steps

Once you have NtfyClip running:

1. **Set Up Multiple Devices**: Install and configure on other devices
2. **Explore Advanced Features**: Try authentication and dual configurations
3. **Monitor Activity**: Use the Logs section for detailed operation tracking
4. **Customize Settings**: Adjust configuration for your specific needs

## 💡 Pro Tips

- **Unique Topics**: Use descriptive, unique topic names to avoid conflicts
- **Security**: Use private ntfy servers for sensitive content
- **Monitoring**: Keep an eye on the beautiful glass-styled counters for activity
- **Performance**: The app is designed to run continuously without impact

---

**Enjoy your beautiful, modern clipboard synchronization experience! 🎉**

For more detailed information, see the full [README.md](README.md) documentation.
