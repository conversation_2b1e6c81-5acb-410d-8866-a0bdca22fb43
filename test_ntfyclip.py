#!/usr/bin/env python3
"""
Test script for NtfyClip functionality
This script tests the rewritten Swift implementation against the working Python version
"""

import requests
import time
import subprocess
import json
import sys
from datetime import datetime

# Test configuration
NTFY_SERVER = "https://ntfy.sh"
TEST_TOPIC = "ntfyclip-test-" + str(int(time.time()))
TEST_MESSAGE = f"Test message from NtfyClip at {datetime.now().strftime('%H:%M:%S')}"

def log(message):
    """Log a message with timestamp"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

def send_test_message():
    """Send a test message to ntfy"""
    log(f"Sending test message to topic: {TEST_TOPIC}")
    try:
        response = requests.post(
            f"{NTFY_SERVER}/{TEST_TOPIC}",
            data=TEST_MESSAGE,
            headers={
                "Title": "NtfyClip Test",
                "Content-Type": "text/plain; charset=utf-8"
            },
            timeout=10
        )
        if response.status_code == 200:
            log("✅ Test message sent successfully")
            return True
        else:
            log(f"❌ Failed to send test message: {response.status_code}")
            return False
    except Exception as e:
        log(f"❌ Error sending test message: {e}")
        return False

def check_clipboard_content():
    """Check current clipboard content"""
    try:
        result = subprocess.run(
            ["pbpaste"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            content = result.stdout
            log(f"📋 Current clipboard: '{content[:50]}{'...' if len(content) > 50 else ''}'")
            return content
        else:
            log("❌ Failed to read clipboard")
            return None
    except Exception as e:
        log(f"❌ Error reading clipboard: {e}")
        return None

def set_clipboard_content(content):
    """Set clipboard content"""
    try:
        result = subprocess.run(
            ["pbcopy"],
            input=content,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            log(f"📋 Set clipboard to: '{content[:50]}{'...' if len(content) > 50 else ''}'")
            return True
        else:
            log("❌ Failed to set clipboard")
            return False
    except Exception as e:
        log(f"❌ Error setting clipboard: {e}")
        return False

def test_clipboard_monitoring():
    """Test clipboard monitoring functionality"""
    log("🧪 Testing clipboard monitoring...")
    
    # Set a test message to clipboard
    test_content = f"Clipboard test at {datetime.now().strftime('%H:%M:%S')}"
    if set_clipboard_content(test_content):
        log("⏳ Waiting 5 seconds for clipboard monitoring to detect change...")
        time.sleep(5)
        log("✅ Clipboard monitoring test completed (check logs for detection)")
        return True
    return False

def test_message_receiving():
    """Test message receiving functionality"""
    log("🧪 Testing message receiving...")
    
    # Send a message and wait for it to appear in clipboard
    if send_test_message():
        log("⏳ Waiting 10 seconds for message to be received...")
        time.sleep(10)
        
        # Check if clipboard was updated
        clipboard_content = check_clipboard_content()
        if clipboard_content and TEST_MESSAGE in clipboard_content:
            log("✅ Message receiving test PASSED - message found in clipboard")
            return True
        else:
            log("❌ Message receiving test FAILED - message not found in clipboard")
            return False
    return False

def main():
    """Main test function"""
    log("🚀 Starting NtfyClip functionality tests")
    log(f"📡 Test topic: {TEST_TOPIC}")
    log(f"🌐 Server: {NTFY_SERVER}")
    
    print("\n" + "="*60)
    print("IMPORTANT: Make sure NtfyClip is running and configured with:")
    print(f"  - Send URL: {NTFY_SERVER}/{TEST_TOPIC}")
    print(f"  - Receive URL: {NTFY_SERVER}/{TEST_TOPIC}")
    print("  - Both sending and receiving enabled")
    print("="*60 + "\n")
    
    input("Press Enter when NtfyClip is configured and running...")
    
    # Test 1: Check initial clipboard state
    log("📋 Checking initial clipboard state...")
    initial_clipboard = check_clipboard_content()
    
    # Test 2: Clipboard monitoring
    test_clipboard_monitoring()
    
    # Test 3: Message receiving
    test_message_receiving()
    
    log("🏁 Test completed!")
    log(f"📝 Test topic used: {TEST_TOPIC}")
    log("💡 Check the NtfyClip logs for detailed information about the backend operations")

if __name__ == "__main__":
    main()
