import AppKit
import OSLog
import Foundation

@MainActor
class ClipboardManager {
    private let pasteboard = NSPasteboard.general
    private let logManager = LogManager.shared

    // MARK: - Change Detection (matching Python implementation)
    private var lastChangeCount: Int = -1

    // MARK: - Image Support Configuration (matching Python macos config)
    private let imageSupport: Bool = true
    private let imageUTIMap: [String: String] = [
        ".png": "public.png",
        ".jpg": "public.jpeg",
        ".jpeg": "public.jpeg",
        ".gif": "com.compuserve.gif",
        ".bmp": "com.microsoft.bmp",
        ".tiff": "public.tiff",
        ".tif": "public.tiff"
    ]

    // MARK: - Initialization (matching Python __init__)
    init() {
        lastChangeCount = pasteboard.changeCount
        logManager.info("ClipboardManager", "Initialized ClipboardManager with change count: \(self.lastChangeCount)")
    }

    // MARK: - Change Detection Methods (matching Python implementation)

    /// Get the current change count of the pasteboard
    func getChangeCount() -> Int {
        return pasteboard.changeCount
    }

    /// Update the stored change count to current (matching Python update_last_change_count)
    func updateLastChangeCount() {
        lastChangeCount = pasteboard.changeCount
        logManager.debug("ClipboardManager", "Updated last change count to: \(self.lastChangeCount)")
    }

    /// Check if clipboard has changed since last check (matching Python has_changed)
    func hasChanged() -> Bool {
        let currentChangeCount = pasteboard.changeCount
        let changed = currentChangeCount != lastChangeCount
        logManager.debug("ClipboardManager", "Clipboard change check: current=\(currentChangeCount), last=\(self.lastChangeCount), changed=\(changed)")
        return changed
    }

    // MARK: - Text Operations (matching Python implementation)

    /// Get text content from the clipboard (matching Python get_text)
    func getText() -> String? {
        guard pasteboard.types?.contains(.string) == true else {
            return nil
        }

        let text = pasteboard.string(forType: .string)
        if let text = text, !text.isEmpty {
            logManager.debug("ClipboardManager", "Retrieved text from clipboard (length: \(text.count))")
            return text
        }
        return nil
    }

    /// Set text content to the clipboard (matching Python set_text)
    func setText(_ text: String, source: String = "ClipboardManager") -> Bool {
        guard !text.isEmpty else {
            logManager.warning("ClipboardManager", "Attempted to set empty text to clipboard from \(source)")
            return false
        }

        pasteboard.clearContents()
        let success = pasteboard.setString(text, forType: .string)

        if success {
            updateLastChangeCount() // Update count after successful write
            logManager.info("ClipboardManager", "Text (length: \(text.count)) set to clipboard by \(source)")
        } else {
            logManager.error("ClipboardManager", "Failed to set text to clipboard from \(source)")
        }

        return success
    }

    // MARK: - Image Operations (matching Python implementation)

    /// Get image content from the clipboard
    func getImage() -> NSImage? {
        guard let imageData = pasteboard.data(forType: .tiff),
              let image = NSImage(data: imageData) else {
            return nil
        }

        logManager.debug("ClipboardManager", "Retrieved image from clipboard")
        return image
    }

    /// Set image content to clipboard using AppleScript (matching Python set_image_macos)
    func setImageMacOS(_ imageData: Data, filename: String, source: String = "ClipboardManager") -> Bool {
        guard imageSupport else {
            logManager.warning("ClipboardManager", "Image setting skipped: Image support disabled")
            return false
        }

        guard !imageData.isEmpty && !filename.isEmpty else {
            logManager.warning("ClipboardManager", "Attempted to set empty image data or missing filename from \(source)")
            return false
        }

        var tempPath: String?
        var success = false

        do {
            let fileExt = (filename as NSString).pathExtension.lowercased()
            let finalExt = fileExt.isEmpty ? "png" : fileExt

            // Create temporary file
            let tempDir = FileManager.default.temporaryDirectory
            let tempFileName = "clipboard_image_\(UUID().uuidString).\(finalExt)"
            let tempURL = tempDir.appendingPathComponent(tempFileName)
            tempPath = tempURL.path

            try imageData.write(to: tempURL)
            logManager.debug("ClipboardManager", "Image data (size: \(imageData.count)) written to temporary file: \(tempURL.path) by \(source)")

            // Use AppleScript to set clipboard (matching Python implementation)
            let appleScriptCommand = "set the clipboard to (read POSIX file \"\(tempURL.path)\" as picture)"
            logManager.info("ClipboardManager", "Executing AppleScript for \(source): set clipboard to (read POSIX file ... as picture)")

            let process = Process()
            process.launchPath = "/usr/bin/osascript"
            process.arguments = ["-e", appleScriptCommand]

            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe

            process.launch()
            process.waitUntilExit()

            if process.terminationStatus == 0 {
                updateLastChangeCount() // Update count after successful write
                logManager.info("ClipboardManager", "Image '\(filename)' successfully set to clipboard via AppleScript by \(source)")
                success = true
            } else {
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                let output = String(data: data, encoding: .utf8) ?? ""
                logManager.error("ClipboardManager", "AppleScript execution failed for \(source) (return code: \(process.terminationStatus))")
                if !output.isEmpty {
                    logManager.error("ClipboardManager", "AppleScript output: \(output)")
                }
            }

        } catch {
            logManager.error("ClipboardManager", "Error setting image to clipboard via AppleScript: \(error.localizedDescription)")
        }

        // Clean up temporary file
        if let tempPath = tempPath {
            try? FileManager.default.removeItem(atPath: tempPath)
            logManager.debug("ClipboardManager", "Temporary image file deleted: \(tempPath)")
        }

        return success
    }

    /// Set image using legacy method (for backward compatibility)
    func setImage(_ image: NSImage) {
        guard let tiffData = image.tiffRepresentation else {
            logManager.error("ClipboardManager", "Failed to get TIFF representation of image")
            return
        }

        pasteboard.clearContents()
        pasteboard.setData(tiffData, forType: .tiff)
        updateLastChangeCount()
        logManager.debug("ClipboardManager", "Set image to clipboard using legacy method")
    }

    // MARK: - Content Operations (enhanced for loop prevention)

    /// Get the current clipboard content as ClipboardContent
    func getCurrentContent() -> ClipboardContent? {
        // Check for text first
        if let text = getText() {
            return ClipboardContent(type: .text, data: text)
        }

        // Check for image
        if let image = getImage() {
            return ClipboardContent(type: .image, data: image)
        }

        return nil
    }

    /// Set clipboard content from ClipboardContent (enhanced with source tracking)
    func setContent(_ content: ClipboardContent, source: String = "ClipboardManager") -> Bool {
        switch content.type {
        case .text:
            if let text = content.data as? String {
                return setText(text, source: source)
            }
        case .image:
            if let image = content.data as? NSImage {
                setImage(image)
                return true
            }
        }
        return false
    }

    // MARK: - Legacy Methods (for backward compatibility)

    /// Check if clipboard has changed since the last check
    func hasChanged(since lastChangeCount: Int) -> Bool {
        return getChangeCount() != lastChangeCount
    }

    /// Get clipboard content as Data for transmission
    func getContentAsData() -> Data? {
        if let text = getText() {
            return text.data(using: .utf8)
        }

        if let image = getImage(),
           let tiffData = image.tiffRepresentation {
            return tiffData
        }

        return nil
    }

    /// Set clipboard content from Data
    func setContentFromData(_ data: Data, type: ClipboardContentType) {
        switch type {
        case .text:
            if let text = String(data: data, encoding: .utf8) {
                _ = setText(text, source: "DataImport")
            }
        case .image:
            if let image = NSImage(data: data) {
                setImage(image)
            }
        }
    }
}

// MARK: - Supporting Types

enum ClipboardContentType: String, CaseIterable {
    case text = "text"
    case image = "image"

    var mimeType: String {
        switch self {
        case .text:
            return "text/plain"
        case .image:
            return "image/tiff"
        }
    }
}

struct ClipboardContent {
    let type: ClipboardContentType
    let data: Any
    let timestamp: Date

    init(type: ClipboardContentType, data: Any) {
        self.type = type
        self.data = data
        self.timestamp = Date()
    }


}

// MARK: - Clipboard Errors

enum ClipboardError: Error, LocalizedError {
    case noContent
    case unsupportedContentType
    case failedToReadContent
    case failedToSetContent
    case imageProcessingFailed
    case appleScriptFailed

    var errorDescription: String? {
        switch self {
        case .noContent:
            return "No content available in clipboard"
        case .unsupportedContentType:
            return "Unsupported content type"
        case .failedToReadContent:
            return "Failed to read clipboard content"
        case .failedToSetContent:
            return "Failed to set clipboard content"
        case .imageProcessingFailed:
            return "Failed to process image data"
        case .appleScriptFailed:
            return "AppleScript execution failed"
        }
    }
}
