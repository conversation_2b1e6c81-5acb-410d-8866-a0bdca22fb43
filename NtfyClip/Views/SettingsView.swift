import SwiftUI

struct SettingsView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @StateObject private var configManager = ConfigurationManager()
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingResetAlert = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerSection
            
            // Content with Liquid Glass background
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    serverConfigSection
                    authenticationSection
                    aboutSection
                }
                .padding(24)
            }
            .background(
                LiquidGlassBackground(
                    colors: [
                        Color.blue.opacity(0.03),
                        Color.purple.opacity(0.02),
                        Color.clear
                    ],
                    intensity: .subtle
                )
            )
            
            // Footer
            footerSection
        }
        .navigationTitle("Settings")
        .frame(minWidth: 500, minHeight: 400)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Image(systemName: "doc.on.clipboard")
                .font(.title)
                .foregroundColor(.blue)
            
            VStack(alignment: .leading) {
                Text("NtfyClip Settings")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Configure your clipboard sync")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            LiquidGlassButton(intensity: .medium, style: .standard, action: {
                dismiss()
            }) {
                Text("Done")
                    .foregroundColor(.white)
                    .fontWeight(.medium)
            }
        }
        .padding()
        .background(
            LiquidGlassEffect(intensity: .medium)
        )
    }
    
    // MARK: - Server Configuration Section
    private var serverConfigSection: some View {
        LiquidGlassCard(intensity: .medium, cornerRadius: 16) {
            VStack(alignment: .leading, spacing: 20) {
                HStack {
                    Image(systemName: "server.rack")
                        .font(.title2)
                        .foregroundColor(.blue)
                    Text("Server Configuration")
                        .font(.title2)
                        .fontWeight(.semibold)
                }

                VStack(alignment: .leading, spacing: 16) {
                    Text("Ntfy Server URL")
                        .font(.headline)
                        .fontWeight(.medium)

                    TextField("https://ntfy.sh", text: $configManager.sendServerURL)
                        .textFieldStyle(.roundedBorder)
                        .frame(height: 36)

                    Text("The URL of your ntfy server")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                VStack(alignment: .leading, spacing: 16) {
                    Text("Topic Name")
                        .font(.headline)
                        .fontWeight(.medium)

                    TextField("my-clipboard-topic", text: $configManager.sendTopicName)
                        .textFieldStyle(.roundedBorder)
                        .frame(height: 36)

                    Text("A unique topic name for your clipboard sync")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // Configuration validation status with Liquid Glass
                LiquidGlassCard(intensity: .subtle, cornerRadius: 12) {
                    HStack(spacing: 12) {
                        Image(systemName: configManager.isConfigurationValid() ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(configManager.isConfigurationValid() ? .green : .red)

                        Text(configManager.isConfigurationValid() ? "Configuration is valid" : "Please complete the configuration")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(configManager.isConfigurationValid() ? .green : .red)

                        Spacer()
                    }
                    .padding(16)
                }
            }
            .padding(24)
        }
    }
    
    // MARK: - Authentication Section
    private var authenticationSection: some View {
        LiquidGlassCard(intensity: .medium, cornerRadius: 16) {
            VStack(alignment: .leading, spacing: 20) {
                HStack {
                    Image(systemName: "lock.shield")
                        .font(.title2)
                        .foregroundColor(.orange)
                    Text("Authentication (Optional)")
                        .font(.title2)
                        .fontWeight(.semibold)
                }

                Toggle("Use Authentication", isOn: $configManager.useSendAuthentication)
                    .toggleStyle(.checkbox)
                    .padding(.vertical, 8)

                if configManager.useSendAuthentication {
                    VStack(alignment: .leading, spacing: 16) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Username")
                                .font(.headline)
                                .fontWeight(.medium)

                            TextField("Username", text: $configManager.sendUsername)
                                .textFieldStyle(.roundedBorder)
                                .frame(height: 36)
                        }

                        VStack(alignment: .leading, spacing: 8) {
                            Text("Password")
                                .font(.headline)
                                .fontWeight(.medium)

                            SecureField("Password", text: $configManager.sendPassword)
                                .textFieldStyle(.roundedBorder)
                                .frame(height: 36)
                        }
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
            .padding(24)
            .animation(.easeInOut(duration: 0.2), value: configManager.useSendAuthentication)
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        LiquidGlassCard(intensity: .medium, cornerRadius: 16) {
            VStack(alignment: .leading, spacing: 20) {
                HStack {
                    Image(systemName: "info.circle")
                        .font(.title2)
                        .foregroundColor(.purple)
                    Text("About")
                        .font(.title2)
                        .fontWeight(.semibold)
                }

                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Text("Version:")
                            .font(.headline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("1.0.0")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Build:")
                            .font(.headline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("1")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }

                    Divider()
                        .opacity(0.5)

                    Text("NtfyClip synchronizes your clipboard content with ntfy, enabling seamless clipboard sharing across devices.")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
            }
            .padding(24)
        }
    }
    
    // MARK: - Footer Section
    private var footerSection: some View {
        HStack(spacing: 16) {
            LiquidGlassButton(intensity: .subtle, style: .standard, action: {
                showingResetAlert = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.counterclockwise")
                        .font(.system(size: 14, weight: .medium))
                    Text("Reset to Defaults")
                        .fontWeight(.medium)
                }
                .foregroundColor(.red)
            }

            Spacer()

            LiquidGlassButton(
                intensity: configManager.isConfigurationValid() ? .medium : .subtle,
                style: .standard,
                action: {
                    testConnection()
                }
            ) {
                HStack(spacing: 8) {
                    Image(systemName: "network")
                        .font(.system(size: 14, weight: .medium))
                    Text("Test Connection")
                        .fontWeight(.medium)
                }
                .foregroundColor(configManager.isConfigurationValid() ? .primary : .secondary)
            }
            .disabled(!configManager.isConfigurationValid())
        }
        .padding(20)
        .background(
            LiquidGlassEffect(intensity: .medium)
        )
        .alert("Reset Settings", isPresented: $showingResetAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                configManager.resetToDefaults()
            }
        } message: {
            Text("This will reset all settings to their default values. This action cannot be undone.")
        }
    }
    
    // MARK: - Private Methods
    private func testConnection() {
        // TODO: Implement connection test
        // This would involve making a test request to the ntfy server
        print("Testing connection to \(configManager.sendServerURL)/\(configManager.sendTopicName)")
    }
}
