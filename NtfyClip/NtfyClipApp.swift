import SwiftUI
import OSLog

@main
struct NtfyClipApp: App {
    @StateObject private var configManager = ConfigurationManager()
    @StateObject private var appViewModel: AppViewModel

    init() {
        let configManager = ConfigurationManager()
        _configManager = StateObject(wrappedValue: configManager)
        _appViewModel = StateObject(wrappedValue: AppViewModel(configurationManager: configManager))
    }

    private let logger = Logger(subsystem: "com.ntfyclip.app", category: "App")

    var body: some Scene {
        // 主窗口 - 新的侧边栏界面
        WindowGroup("NtfyClip", id: "main") {
            MainWindowView()
                .environmentObject(appViewModel)
                .environmentObject(configManager)
                .frame(minWidth: 700, minHeight: 500)
                .onAppear {
                    logger.info("Main window appeared")
                    print("🪟 Main window is now visible")
                }
        }
        .windowResizability(.contentMinSize)
        .defaultPosition(.center)
        .defaultSize(width: 800, height: 600)

        // 菜单栏额外项 - 快速访问界面
        MenuBarExtra("NtfyClip", systemImage: "doc.on.clipboard") {
            MenuBarView()
                .environmentObject(appViewModel)
                .environmentObject(configManager)
        }
        .menuBarExtraStyle(.window)
    }
}