<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions for modern look -->
    <linearGradient id="clipboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007AFF;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#5856D6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#AF52DE;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="paperGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F2F2F7;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="clipGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8E8E93;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#636366;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    
    <!-- Inner shadow for depth -->
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="4" result="offset-blur"/>
      <feFlood flood-color="#000000" flood-opacity="0.1"/>
      <feComposite in2="offset-blur" operator="in"/>
    </filter>
  </defs>
  
  <!-- Background circle for app icon -->
  <circle cx="512" cy="512" r="480" fill="url(#clipboardGradient)" filter="url(#shadow)"/>
  
  <!-- Main clipboard body -->
  <rect x="256" y="180" width="512" height="664" rx="48" ry="48" 
        fill="url(#paperGradient)" 
        stroke="#E5E5EA" 
        stroke-width="4" 
        filter="url(#innerShadow)"/>
  
  <!-- Clipboard clip/handle -->
  <rect x="384" y="120" width="256" height="120" rx="24" ry="24" 
        fill="url(#clipGradient)" 
        stroke="#48484A" 
        stroke-width="3"/>
  
  <!-- Inner clip detail -->
  <rect x="408" y="144" width="208" height="72" rx="12" ry="12" 
        fill="#C7C7CC" 
        stroke="#AEAEB2" 
        stroke-width="2"/>
  
  <!-- Document lines to represent content -->
  <g opacity="0.6">
    <!-- Line 1 -->
    <rect x="320" y="320" width="320" height="16" rx="8" fill="#007AFF" opacity="0.8"/>
    
    <!-- Line 2 -->
    <rect x="320" y="380" width="280" height="16" rx="8" fill="#34C759" opacity="0.7"/>
    
    <!-- Line 3 -->
    <rect x="320" y="440" width="360" height="16" rx="8" fill="#FF9500" opacity="0.7"/>
    
    <!-- Line 4 -->
    <rect x="320" y="500" width="240" height="16" rx="8" fill="#FF3B30" opacity="0.6"/>
    
    <!-- Line 5 -->
    <rect x="320" y="560" width="300" height="16" rx="8" fill="#5856D6" opacity="0.6"/>
  </g>
  
  <!-- Sync/notification indicator -->
  <circle cx="680" cy="280" r="48" fill="#34C759" stroke="#FFFFFF" stroke-width="6"/>
  
  <!-- Checkmark in sync indicator -->
  <path d="M 660 280 L 675 295 L 700 265" 
        stroke="#FFFFFF" 
        stroke-width="8" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        fill="none"/>
  
  <!-- Subtle highlight on clipboard for depth -->
  <rect x="256" y="180" width="512" height="100" rx="48" ry="48" 
        fill="url(#paperGradient)" 
        opacity="0.3"/>
</svg>
