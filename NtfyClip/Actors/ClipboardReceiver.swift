import Foundation
import OSLog
import AppKit

actor ClipboardReceiver {
    // MARK: - Properties (matching Python receiver.py)
    private weak var appViewModel: AppViewModel?
    private let ntfyService: NtfyService
    private let clipboardManager: ClipboardManager
    private let configurationManager: ConfigurationManager
    private let logManager = LogManager.shared

    // MARK: - State Management (matching Python implementation)
    private var isReceiving = false
    private var receivingTask: Task<Void, Never>?
    private var webSocketTask: URLSessionWebSocketTask?

    // MARK: - Configuration (matching Python config)
    private let reconnectDelay: TimeInterval = 5.0 // Reconnect delay in seconds (matching Python)
    private let requestTimeout: TimeInterval = 15.0 // Request timeout (matching Python)
    private let isMacOSImageSupport: Bool = true // Image support enabled (matching Python)

    // MARK: - Error Types
    enum CriticalError: Error {
        case invalidConfiguration
        case webSocketConnectionFailed
    }

    // MARK: - Initialization
    init(appViewModel: AppViewModel,
         ntfyService: NtfyService,
         clipboardManager: ClipboardManager,
         configurationManager: ConfigurationManager) {
        self.appViewModel = appViewModel
        self.ntfyService = ntfyService
        self.clipboardManager = clipboardManager
        self.configurationManager = configurationManager

        logManager.info("ClipboardReceiver", "ClipboardReceiver initialized")
        if isMacOSImageSupport {
            logManager.info("ClipboardReceiver", "macOS image support is enabled")
        }
    }

    // MARK: - Public Methods (matching Python run method)
    func startReceiving() async {
        logManager.info("ClipboardReceiver", "startReceiving() called")

        guard !isReceiving else {
            logManager.warning("ClipboardReceiver", "Already receiving messages")
            return
        }

        logManager.info("ClipboardReceiver", "Checking if receiving is enabled: \(configurationManager.enableReceiving)")
        guard configurationManager.enableReceiving else {
            logManager.info("ClipboardReceiver", "Ntfy Receiver is disabled in configuration")
            return
        }

        let isConfigValid = configurationManager.isReceiveConfigurationValid()
        logManager.info("ClipboardReceiver", "Checking if receive configuration is valid: \(isConfigValid)")
        guard isConfigValid else {
            logManager.error("ClipboardReceiver", "Receiver is enabled but configuration is invalid. Disabling receiver.")
            logManager.error("ClipboardReceiver", "Receive server URL: '\(configurationManager.receiveServerURL)'")
            logManager.error("ClipboardReceiver", "Receive topic name: '\(configurationManager.receiveTopicName)'")
            return
        }

        isReceiving = true
        logManager.info("ClipboardReceiver", "Starting WebSocket listening loop with reconnection logic")

        receivingTask = Task {
            await runWebSocketLoop()
        }
    }

    func stopReceiving() async {
        guard isReceiving else {
            logManager.debug("ClipboardReceiver", "Receiver is not currently running")
            return
        }

        isReceiving = false
        logManager.info("ClipboardReceiver", "Stopping WebSocket receiver...")

        // Close WebSocket connection
        webSocketTask?.cancel()
        webSocketTask = nil

        receivingTask?.cancel()
        receivingTask = nil

        logManager.info("ClipboardReceiver", "WebSocket receiver stopped")
    }

    // MARK: - Private Methods (matching Python implementation)

    /// Main WebSocket loop with reconnection logic (matching Python run method)
    private func runWebSocketLoop() async {
        while isReceiving && !Task.isCancelled {
            do {
                logManager.info("ClipboardReceiver", "Attempting to connect to WebSocket")

                // Get topic URL for WebSocket connection
                guard let topicURL = configurationManager.getReceiveTopicURL() else {
                    logManager.critical("ClipboardReceiver", "Invalid WebSocket URL. Receiver stopping.")
                    isReceiving = false
                    break
                }

                let authHeader = configurationManager.getReceiveAuthenticationHeader()
                logManager.debug("ClipboardReceiver", "WebSocket URL: \(topicURL.absoluteString)")

                // Connect to WebSocket
                webSocketTask = try await ntfyService.connectWebSocket(to: topicURL, authHeader: authHeader)

                logManager.info("ClipboardReceiver", "Successfully connected to ntfy topic via WebSocket")

                // Handle messages
                await handleWebSocketMessages()

            } catch is CancellationError {
                logManager.info("ClipboardReceiver", "Receiver task cancelled during shutdown")
                isReceiving = false
                break
            } catch {
                logManager.error("ClipboardReceiver", "WebSocket connection error: \(error.localizedDescription). Retrying in \(self.reconnectDelay)s...")
            }

            // Clean up WebSocket
            webSocketTask?.cancel()
            webSocketTask = nil

            // Wait before reconnecting, only if still enabled and not cancelled
            if isReceiving && !Task.isCancelled {
                logManager.debug("ClipboardReceiver", "Waiting \(self.reconnectDelay)s before reconnecting...")
                do {
                    try await Task.sleep(nanoseconds: UInt64(self.reconnectDelay * 1_000_000_000))
                } catch is CancellationError {
                    logManager.info("ClipboardReceiver", "Receiver reconnect sleep interrupted by cancellation")
                    isReceiving = false
                    break
                } catch {
                    // Handle other sleep errors
                    logManager.warning("ClipboardReceiver", "Sleep interrupted: \(error.localizedDescription)")
                    break
                }
            }
        }

        logManager.info("ClipboardReceiver", "Ntfy Receiver run loop finished")
    }

    /// Handle incoming WebSocket messages (matching Python handle_messages)
    private func handleWebSocketMessages() async {
        guard let webSocket = webSocketTask else {
            logManager.warning("ClipboardReceiver", "WebSocket task is nil, cannot handle messages")
            return
        }

        logManager.debug("ClipboardReceiver", "Starting to handle WebSocket messages")
        do {
            while isReceiving && !Task.isCancelled {
                let message = try await webSocket.receive()

                switch message {
                case .string(let text):
                    logManager.debug("ClipboardReceiver", "Received string message: \(text.prefix(100))...")
                    await processWebSocketMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        logManager.debug("ClipboardReceiver", "Received data message (converted to string): \(text.prefix(100))...")
                        await processWebSocketMessage(text)
                    } else {
                        logManager.warning("ClipboardReceiver", "Received non-UTF8 data message")
                    }
                @unknown default:
                    logManager.warning("ClipboardReceiver", "Received unknown message type")
                }
            }
        } catch is CancellationError {
            logManager.info("ClipboardReceiver", "Receiver message handling loop cancelled")
        } catch {
            logManager.warning("ClipboardReceiver", "WebSocket connection closed while handling messages: \(error.localizedDescription)")
        }
    }

    /// Process individual WebSocket message (matching Python process_ntfy_message)
    private func processWebSocketMessage(_ messageText: String) async {
        do {
            guard let messageData = messageText.data(using: .utf8) else {
                logManager.warning("ClipboardReceiver", "Failed to convert message to data")
                return
            }

            let jsonObject = try JSONSerialization.jsonObject(with: messageData) as? [String: Any]
            guard let json = jsonObject else {
                logManager.warning("ClipboardReceiver", "Failed to parse JSON message: \(messageText.prefix(200))...")
                return
            }

            let event = json["event"] as? String ?? ""
            logManager.debug("ClipboardReceiver", "Processing event: \(event)")

            switch event {
            case "message":
                await processNtfyMessage(json)
            case "keepalive":
                logManager.debug("ClipboardReceiver", "Received keepalive")
            case "open":
                logManager.info("ClipboardReceiver", "WebSocket connection confirmed open by ntfy")
            case "poll_request":
                logManager.debug("ClipboardReceiver", "Received poll_request signal")
            default:
                logManager.warning("ClipboardReceiver", "Received unknown event type: \(event)")
            }

        } catch {
            logManager.warning("ClipboardReceiver", "Failed to decode JSON message: \(messageText.prefix(200))...")
        }
    }

    /// Process ntfy message event (matching Python process_ntfy_message)
    private func processNtfyMessage(_ data: [String: Any]) async {
        let messageId = data["id"] as? String ?? "N/A"
        let messageContent = data["message"] as? String ?? ""
        let attachment = data["attachment"] as? [String: Any]
        let title = data["title"] as? String ?? ""

        logManager.info("ClipboardReceiver", "Received message (ID: \(messageId), Title: '\(title.prefix(30))...', Content: '\(messageContent.prefix(50))...')")

        var textToCopy: String?
        var imageToCopy: Data?
        var imageFilename: String?
        var copySourceDescription = "Unknown"

        // Prioritize Attachment (matching Python implementation)
        if let attachment = attachment {
            let attachUrl = attachment["url"] as? String
            let attachName = attachment["name"] as? String
            let attachType = attachment["type"] as? String
            let attachSize = attachment["size"] as? Int

            if let attachUrl = attachUrl, let attachName = attachName {
                logManager.info("ClipboardReceiver", "Message has attachment: '\(attachName)' (Type: \(attachType ?? "N/A"), Size: \(attachSize ?? 0))")

                // Download attachment
                do {
                    let baseServerURL = configurationManager.receiveServerURL
                    logManager.debug("ClipboardReceiver", "Downloading attachment from: \(attachUrl)")
                    let (contentBytes, contentTypeHeader) = try await ntfyService.downloadAttachment(from: attachUrl, baseServerURL: baseServerURL)
                    let resolvedContentType = attachType ?? contentTypeHeader
                    logManager.debug("ClipboardReceiver", "Downloaded \(contentBytes.count) bytes, content type: \(resolvedContentType ?? "unknown")")

                    // Check if it's an image
                    if ntfyService.isImageAttachment(filename: attachName, contentType: resolvedContentType) {
                        if isMacOSImageSupport {
                            logManager.info("ClipboardReceiver", "Detected image attachment '\(attachName)'. Preparing to copy image (macOS)")
                            imageToCopy = contentBytes
                            imageFilename = attachName
                            copySourceDescription = "Image Attachment '\(attachName)'"
                        } else {
                            logManager.info("ClipboardReceiver", "Detected image attachment '\(attachName)'. Copying URL (non-macOS or disabled)")
                            textToCopy = attachUrl
                            copySourceDescription = "Image Attachment URL '\(attachName)'"
                        }
                    }
                    // Check if it's a text file
                    else if ntfyService.isTextAttachment(filename: attachName, contentType: resolvedContentType) {
                        logManager.info("ClipboardReceiver", "Detected text attachment '\(attachName)'. Decoding content")
                        textToCopy = ntfyService.decodeTextContent(contentBytes, url: attachUrl)
                        copySourceDescription = "Text Attachment '\(attachName)'"
                        if textToCopy == nil {
                            logManager.warning("ClipboardReceiver", "Failed to decode text attachment '\(attachName)'. Falling back to message body")
                            textToCopy = messageContent
                            copySourceDescription = "Message Body (Text attach decode failed: '\(attachName)')"
                        }
                    }
                    // Unknown attachment type
                    else {
                        logManager.info("ClipboardReceiver", "Attachment '\(attachName)' is not a recognized image or text type. Copying message body if available")
                        if !messageContent.isEmpty {
                            textToCopy = messageContent
                            copySourceDescription = "Message Body (Unknown attach type: '\(attachName)')"
                        } else {
                            logManager.warning("ClipboardReceiver", "Unknown attachment type '\(attachName)' and no message body. Nothing to copy")
                            return
                        }
                    }

                } catch {
                    logManager.warning("ClipboardReceiver", "Failed to download attachment '\(attachName)': \(error.localizedDescription). Falling back to message body if available")
                    if !messageContent.isEmpty {
                        textToCopy = messageContent
                        copySourceDescription = "Message Body (Attach download failed: '\(attachName)')"
                    } else {
                        logManager.warning("ClipboardReceiver", "Attachment download failed for '\(attachName)' and no message body. Nothing to copy")
                        return
                    }
                }
            } else {
                logManager.warning("ClipboardReceiver", "Message has incomplete attachment data. Copying message body if available")
                if !messageContent.isEmpty {
                    textToCopy = messageContent
                    copySourceDescription = "Message Body (Incomplete attach data)"
                } else {
                    logManager.warning("ClipboardReceiver", "Incomplete attachment data and no message body. Nothing to copy")
                    return
                }
            }
        }
        // No Attachment or Fallback
        else {
            if !messageContent.isEmpty {
                logManager.info("ClipboardReceiver", "Received message with no attachment or fell back to message body")
                textToCopy = messageContent
                copySourceDescription = "Message Body"
            } else {
                logManager.info("ClipboardReceiver", "Received message with no attachment and no message body. Nothing to copy")
                return
            }
        }

        // Perform Clipboard Action
        var copiedSuccessfully = false

        // Handle image copying
        if let imageData = imageToCopy, let filename = imageFilename, isMacOSImageSupport {
            logManager.info("ClipboardReceiver", "Attempting to copy \(copySourceDescription) to clipboard (macOS image)...")
            copiedSuccessfully = await MainActor.run {
                clipboardManager.setImageMacOS(imageData, filename: filename, source: "Receiver")
            }
            if copiedSuccessfully {
                logManager.info("ClipboardReceiver", "Successfully copied \(copySourceDescription) to clipboard")
            } else {
                logManager.error("ClipboardReceiver", "Failed to copy \(copySourceDescription) (image) to clipboard")
            }
        }

        // Handle text copying (either primary or fallback)
        if let text = textToCopy, !copiedSuccessfully {
            logManager.info("ClipboardReceiver", "Attempting to copy \(copySourceDescription) to clipboard (text)...")
            copiedSuccessfully = await MainActor.run {
                clipboardManager.setText(text, source: "Receiver")
            }
            if copiedSuccessfully {
                // Update shared state for loop prevention (matching Python)
                await appViewModel?.didReceiveContent(identifier: text.sha256)
                logManager.info("ClipboardReceiver", "Successfully copied \(copySourceDescription) to clipboard. Updated last received content")
            } else {
                logManager.error("ClipboardReceiver", "Failed to copy \(copySourceDescription) (text) to clipboard")
            }
        }
    }
}

// MARK: - ClipboardReceiver Extensions

extension ClipboardReceiver {
    /// Get current receiving status
    var isCurrentlyReceiving: Bool {
        return isReceiving
    }
}
