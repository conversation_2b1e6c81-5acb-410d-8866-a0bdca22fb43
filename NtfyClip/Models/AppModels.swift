import Foundation
import AppKit

// MARK: - App State Models

/// Represents the overall state of the application
struct AppState {
    var isConnected: Bool = false
    var isSyncEnabled: Bool = false
    var lastSyncTime: Date?
    var clipboardItemsCount: Int = 0
    var errorMessage: String?
    
    var connectionStatus: ConnectionStatus {
        if errorMessage != nil {
            return .error
        } else if isConnected {
            return .connected
        } else if isSyncEnabled {
            return .connecting
        } else {
            return .disconnected
        }
    }
}

/// Connection status enumeration
enum ConnectionStatus {
    case disconnected
    case connecting
    case connected
    case error
    
    var displayText: String {
        switch self {
        case .disconnected:
            return "Disconnected"
        case .connecting:
            return "Connecting..."
        case .connected:
            return "Connected"
        case .error:
            return "Error"
        }
    }
    
    var color: NSColor {
        switch self {
        case .disconnected:
            return .systemGray
        case .connecting:
            return .systemOrange
        case .connected:
            return .systemGreen
        case .error:
            return .systemRed
        }
    }
}

// MARK: - Configuration Models

/// Application configuration structure
struct AppConfiguration {
    var ntfyServerURL: String = "https://ntfy.sh"
    var topicName: String = ""
    var isSyncEnabled: Bool = false
    var useAuthentication: Bool = false
    var username: String = ""
    var password: String = ""
    
    /// Check if the configuration is valid for operation
    var isValid: Bool {
        return !ntfyServerURL.isEmpty &&
               !topicName.isEmpty &&
               isValidURL(ntfyServerURL)
    }

    private func isValidURL(_ urlString: String) -> Bool {
        guard let url = URL(string: urlString) else { return false }
        return url.scheme == "http" || url.scheme == "https"
    }
    
    /// Get the full topic URL
    var fullTopicURL: URL? {
        guard isValid else { return nil }
        
        let baseURL = ntfyServerURL.hasSuffix("/") ? String(ntfyServerURL.dropLast()) : ntfyServerURL
        let urlString = "\(baseURL)/\(topicName)"
        
        return URL(string: urlString)
    }
    
    /// Get authentication header if authentication is enabled
    var authenticationHeader: String? {
        guard useAuthentication, !username.isEmpty, !password.isEmpty else {
            return nil
        }
        
        let credentials = "\(username):\(password)"
        guard let credentialsData = credentials.data(using: .utf8) else {
            return nil
        }
        
        return "Basic \(credentialsData.base64EncodedString())"
    }
}

// MARK: - Clipboard Models

/// Represents clipboard content with metadata
struct ClipboardItem {
    let id: String
    let content: ClipboardContent
    let timestamp: Date
    let source: ClipboardSource
    
    init(content: ClipboardContent, source: ClipboardSource = .local) {
        self.id = UUID().uuidString
        self.content = content
        self.timestamp = Date()
        self.source = source
    }
}

/// Source of clipboard content
enum ClipboardSource {
    case local      // Content originated from local clipboard
    case remote     // Content received from ntfy
    
    var displayName: String {
        switch self {
        case .local:
            return "Local"
        case .remote:
            return "Remote"
        }
    }
}

// MARK: - Sync Models

/// Represents a sync operation
struct SyncOperation {
    let id: String
    let type: SyncOperationType
    let content: ClipboardContent
    let timestamp: Date
    var status: SyncOperationStatus
    var error: Error?
    
    init(type: SyncOperationType, content: ClipboardContent) {
        self.id = UUID().uuidString
        self.type = type
        self.content = content
        self.timestamp = Date()
        self.status = .pending
    }
}

/// Type of sync operation
enum SyncOperationType {
    case send       // Sending to ntfy
    case receive    // Receiving from ntfy
    
    var displayName: String {
        switch self {
        case .send:
            return "Send"
        case .receive:
            return "Receive"
        }
    }
}

/// Status of sync operation
enum SyncOperationStatus {
    case pending
    case inProgress
    case completed
    case failed
    
    var displayName: String {
        switch self {
        case .pending:
            return "Pending"
        case .inProgress:
            return "In Progress"
        case .completed:
            return "Completed"
        case .failed:
            return "Failed"
        }
    }
}

// MARK: - Statistics Models

/// Application usage statistics
struct AppStatistics {
    var totalItemsSynced: Int = 0
    var totalItemsSent: Int = 0
    var totalItemsReceived: Int = 0
    var lastSyncTime: Date?
    var uptime: TimeInterval = 0
    var startTime: Date = Date()
    
    /// Get formatted uptime string
    var formattedUptime: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: uptime) ?? "0s"
    }
    
    /// Get sync rate (items per hour)
    var syncRate: Double {
        guard uptime > 0 else { return 0 }
        return Double(totalItemsSynced) / (uptime / 3600)
    }
}

// MARK: - Error Models

/// Application-specific errors
enum AppError: Error, LocalizedError {
    case configurationInvalid
    case clipboardAccessFailed
    case networkError(Error)
    case authenticationFailed
    case serverError(Int)
    case unknownError(Error)
    
    var errorDescription: String? {
        switch self {
        case .configurationInvalid:
            return "Configuration is invalid. Please check your settings."
        case .clipboardAccessFailed:
            return "Failed to access clipboard. Please check permissions."
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .authenticationFailed:
            return "Authentication failed. Please check your credentials."
        case .serverError(let code):
            return "Server error: HTTP \(code)"
        case .unknownError(let error):
            return "Unknown error: \(error.localizedDescription)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .configurationInvalid:
            return "Open Settings and verify your server URL and topic name."
        case .clipboardAccessFailed:
            return "Grant clipboard access permissions in System Preferences."
        case .networkError:
            return "Check your internet connection and try again."
        case .authenticationFailed:
            return "Verify your username and password in Settings."
        case .serverError:
            return "Check if the ntfy server is accessible."
        case .unknownError:
            return "Try restarting the application."
        }
    }
}
