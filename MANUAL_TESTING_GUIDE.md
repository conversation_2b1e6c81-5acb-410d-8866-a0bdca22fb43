# 🧪 NtfyClip Manual Testing Guide

## 📋 Overview

This guide will help you manually test the new features of NtfyClip, including:
- ✅ Dual ntfy address configuration system
- ✅ macOS-style sidebar interface
- ✅ Status, Settings, and Logs pages
- ✅ Enhanced logging system
- ✅ Improved loop protection

## 🚀 Getting Started

### 1. Launch the Application

```bash
# If the application is not running, launch it with:
make run
```

### 2. Verify the Application Interface

#### 2.1 Main Window Checklist
- [ ] The main window should appear with the title "NtfyClip".
- [ ] The window should have a sidebar with "Status", "Settings", and "Logs".

#### 2.2 Sidebar Checklist
- [ ] The sidebar should have three items: "Status", "Settings", and "Logs".
- [ ] Clicking on each item should switch the detail view on the right.

#### 2.3 Menu Bar Icon Checklist
- [ ] A clipboard icon should be visible in the menu bar.
- [ ] Clicking the icon should show a quick access menu.

## 📊 Status Page Testing

- [ ] The page should display the overall status of the application.
- [ ] It should show the status of the send and receive services.
- [ ] It should show the validity of the send and receive configurations.
- [ ] It should display statistics, such as the number of synced items.

## ⚙️ Settings Page Testing

- [ ] The page should have tabs for "General", "Send", "Receive", and "Advanced".
- [ ] You should be able to configure the send and receive settings independently.
- [ ] You should be able to enable/disable sending and receiving.
- [ ] You should be able to save your configuration.

## 📝 Logs Page Testing

- [ ] The page should display a list of log entries.
- [ ] You should be able to filter the logs by level.
- [ ] You should be able to search for specific log messages.
- [ ] You should be able to export the logs.

## 🔧 Functional Testing

### 6.1 Dual Address Configuration
1.  **Configure different send and receive addresses** in the Settings tab.
2.  **Verify that the configuration is saved correctly**.
3.  **Verify that the application uses the correct addresses** for sending and receiving.

### 6.2 Loop Protection
1.  **Set the send and receive topics to the same value**.
2.  **Start syncing**.
3.  **Copy some text to the clipboard**.
4.  **Verify that the application does not enter an infinite loop**.

## 🐛 Error Handling Testing

- [ ] Enter an invalid URL and verify that the configuration is marked as invalid.
- [ ] Disconnect from the network and try to sync. Verify that an appropriate error message is displayed.
