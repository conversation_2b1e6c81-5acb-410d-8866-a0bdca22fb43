import XCTest
@testable import NtfyClip

final class NtfyClipTests: XCTestCase {
    
    // MARK: - Configuration Manager Tests
    
    func testConfigurationManagerInitialization() {
        let configManager = ConfigurationManager()
        
        XCTAssertEqual(configManager.ntfyServerURL, "https://ntfy.sh")
        XCTAssertEqual(configManager.topicName, "")
        XCTAssertFalse(configManager.isSyncEnabled)
        XCTAssertFalse(configManager.useAuthentication)
    }
    
    func testConfigurationValidation() {
        let configManager = ConfigurationManager()

        // Initially invalid (empty topic)
        XCTAssertFalse(configManager.isConfigurationValid())

        // Set topic name
        configManager.topicName = "test-topic"
        XCTAssertTrue(configManager.isConfigurationValid())

        // Invalid URL
        configManager.ntfyServerURL = "not-a-valid-url"
        XCTAssertFalse(configManager.isConfigurationValid())

        // Valid URL
        configManager.ntfyServerURL = "https://ntfy.sh"
        XCTAssertTrue(configManager.isConfigurationValid())
    }
    
    func testFullTopicURL() {
        let configManager = ConfigurationManager()
        configManager.ntfyServerURL = "https://ntfy.sh"
        configManager.topicName = "test-topic"
        
        let url = configManager.getFullTopicURL()
        XCTAssertNotNil(url)
        XCTAssertEqual(url?.absoluteString, "https://ntfy.sh/test-topic")
        
        // Test with trailing slash
        configManager.ntfyServerURL = "https://ntfy.sh/"
        let urlWithSlash = configManager.getFullTopicURL()
        XCTAssertEqual(urlWithSlash?.absoluteString, "https://ntfy.sh/test-topic")
    }
    
    func testAuthenticationHeader() {
        let configManager = ConfigurationManager()
        
        // No auth by default
        XCTAssertNil(configManager.getAuthenticationHeader())
        
        // Enable auth but no credentials
        configManager.useAuthentication = true
        XCTAssertNil(configManager.getAuthenticationHeader())
        
        // Add credentials
        configManager.username = "testuser"
        configManager.password = "testpass"
        
        let authHeader = configManager.getAuthenticationHeader()
        XCTAssertNotNil(authHeader)
        XCTAssertTrue(authHeader!.hasPrefix("Basic "))
        
        // Decode and verify
        let base64Part = String(authHeader!.dropFirst(6)) // Remove "Basic "
        let decodedData = Data(base64Encoded: base64Part)
        XCTAssertNotNil(decodedData)
        
        let decodedString = String(data: decodedData!, encoding: .utf8)
        XCTAssertEqual(decodedString, "testuser:testpass")
    }
    
    // MARK: - Content Identifier Tests
    
    func testContentIdentifierGeneration() {
        let textContent = ClipboardContent(type: .text, data: "Hello, World!")
        let identifier = ContentIdentifier.generate(for: textContent)
        
        XCTAssertFalse(identifier.isEmpty)
        XCTAssertEqual(identifier.count, 64) // SHA256 is 64 hex characters
        XCTAssertTrue(ContentIdentifier.isValidIdentifier(identifier))
    }
    
    func testContentIdentifierConsistency() {
        let textContent1 = ClipboardContent(type: .text, data: "Hello, World!")
        let textContent2 = ClipboardContent(type: .text, data: "Hello, World!")
        
        let identifier1 = ContentIdentifier.generate(for: textContent1)
        let identifier2 = ContentIdentifier.generate(for: textContent2)
        
        // Same content should produce same identifier
        XCTAssertEqual(identifier1, identifier2)
    }
    
    func testContentIdentifierUniqueness() {
        let textContent1 = ClipboardContent(type: .text, data: "Hello, World!")
        let textContent2 = ClipboardContent(type: .text, data: "Hello, Universe!")
        
        let identifier1 = ContentIdentifier.generate(for: textContent1)
        let identifier2 = ContentIdentifier.generate(for: textContent2)
        
        // Different content should produce different identifiers
        XCTAssertNotEqual(identifier1, identifier2)
    }
    
    func testIdentifierValidation() {
        // Valid SHA256 hash
        let validId = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
        XCTAssertTrue(ContentIdentifier.isValidIdentifier(validId))
        
        // Invalid length
        let shortId = "abc123"
        XCTAssertFalse(ContentIdentifier.isValidIdentifier(shortId))
        
        // Invalid characters
        let invalidId = "g665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
        XCTAssertFalse(ContentIdentifier.isValidIdentifier(invalidId))
        
        // Empty string
        XCTAssertFalse(ContentIdentifier.isValidIdentifier(""))
    }
    
    // MARK: - App Models Tests
    
    func testAppConfigurationValidation() {
        var config = AppConfiguration()

        // Initially invalid (empty topic)
        XCTAssertFalse(config.isValid)

        // Set topic name
        config.topicName = "test-topic"
        XCTAssertTrue(config.isValid)

        // Invalid URL
        config.ntfyServerURL = "not-a-valid-url"
        XCTAssertFalse(config.isValid)

        // Valid URL again
        config.ntfyServerURL = "https://ntfy.sh"
        XCTAssertTrue(config.isValid)
    }
    
    func testConnectionStatus() {
        var appState = AppState()
        
        // Initially disconnected
        XCTAssertEqual(appState.connectionStatus, .disconnected)
        
        // Enable sync
        appState.isSyncEnabled = true
        XCTAssertEqual(appState.connectionStatus, .connecting)
        
        // Connect
        appState.isConnected = true
        XCTAssertEqual(appState.connectionStatus, .connected)
        
        // Error
        appState.errorMessage = "Test error"
        XCTAssertEqual(appState.connectionStatus, .error)
    }
    
    func testClipboardItem() {
        let content = ClipboardContent(type: .text, data: "Test content")
        let item = ClipboardItem(content: content, source: .local)
        
        XCTAssertFalse(item.id.isEmpty)
        XCTAssertEqual(item.source, .local)
        XCTAssertEqual(item.content.type, .text)
    }
    
    func testSyncOperation() {
        let content = ClipboardContent(type: .text, data: "Test content")
        let operation = SyncOperation(type: .send, content: content)
        
        XCTAssertFalse(operation.id.isEmpty)
        XCTAssertEqual(operation.type, .send)
        XCTAssertEqual(operation.status, .pending)
        XCTAssertNil(operation.error)
    }
    
    // MARK: - Performance Tests
    
    func testContentIdentifierPerformance() {
        let content = ClipboardContent(type: .text, data: "Performance test content")
        
        measure {
            for _ in 0..<1000 {
                _ = ContentIdentifier.generate(for: content)
            }
        }
    }
}
