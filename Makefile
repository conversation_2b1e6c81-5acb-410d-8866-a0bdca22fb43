# NtfyClip Makefile

.PHONY: build run test clean install help

# Default target
help:
	@echo "NtfyClip Build System"
	@echo "====================="
	@echo ""
	@echo "Available targets:"
	@echo "  build     - Build the project"
	@echo "  run       - Build and run the project"
	@echo "  test      - Run tests"
	@echo "  clean     - Clean build artifacts"
	@echo "  install   - Install to Applications folder"
	@echo "  help      - Show this help message"

# Build the project
build:
	@echo "Building NtfyClip..."
	@xcodebuild -scheme NtfyClip -configuration Release -derivedDataPath .build

# Build and run
run: build
	@echo "Running NtfyClip..."
	@open .build/Build/Products/Release/NtfyClip.app

# Run tests
test:
	@echo "Running tests..."
	swift test

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	swift package clean
	rm -rf .build

# Install to Applications folder (requires building first)install: build	@echo "Installing NtfyClip to Applications folder..."	@if [ -d ".build/Build/Products/Release/NtfyClip.app" ]; then \	cp -r .build/Build/Products/Release/NtfyClip.app /Applications/; \	echo "NtfyClip installed to /Applications/"; \	else \	echo "Build not found. Run 'make build' first."; \	exit 1; \	fi

# Development helpers
dev-run:
	@echo "Running in development mode with debug output..."
	swift run --configuration debug

format:
	@echo "Formatting Swift code..."
	@if command -v swiftformat >/dev/null 2>&1; then \
		swiftformat .; \
	else \
		echo "swiftformat not found. Install with: brew install swiftformat"; \
	fi

lint:
	@echo "Linting Swift code..."
	@if command -v swiftlint >/dev/null 2>&1; then \
		swiftlint; \
	else \
		echo "swiftlint not found. Install with: brew install swiftlint"; \
	fi
