#!/bin/bash

# NtfyClip 新功能测试脚本
# 测试双 ntfy 地址配置和新的侧边栏界面

echo "🧪 NtfyClip 新功能测试脚本"
echo "=================================="

# 检查应用程序是否运行
echo "📱 检查应用程序状态..."
if pgrep -f "NtfyClip" > /dev/null; then
    echo "✅ NtfyClip 正在运行"
    echo "📊 进程信息："
    ps aux | grep NtfyClip | grep -v grep
else
    echo "❌ NtfyClip 未运行"
    echo "🚀 启动应用程序..."
    open /Users/<USER>/Library/Developer/Xcode/DerivedData/NtfyClip-*/Build/Products/Debug/NtfyClip.app
    sleep 3
fi

echo ""
echo "🔍 检查应用程序文件结构..."

# 检查新增的文件是否存在
echo "📁 检查新增的视图文件："
files=(
    "NtfyClip/Views/StatusDetailView.swift"
    "NtfyClip/Views/SettingsDetailView.swift"
    "NtfyClip/Views/LogsDetailView.swift"
    "NtfyClip/Services/LogManager.swift"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
    fi
done

echo ""
echo "🔧 检查配置系统更新..."

# 检查 ConfigurationManager 是否包含新的双地址配置
if grep -q "sendServerURL" NtfyClip/Services/ConfigurationManager.swift; then
    echo "✅ 双地址配置系统已实现"
    echo "   - sendServerURL: $(grep -c "sendServerURL" NtfyClip/Services/ConfigurationManager.swift) 次引用"
    echo "   - receiveServerURL: $(grep -c "receiveServerURL" NtfyClip/Services/ConfigurationManager.swift) 次引用"
else
    echo "❌ 双地址配置系统未找到"
fi

echo ""
echo "🎨 检查新界面实现..."

# 检查 MainWindowView 是否使用了 NavigationSplitView
if grep -q "NavigationSplitView" NtfyClip/Views/MainWindowView.swift; then
    echo "✅ 侧边栏界面已实现 (NavigationSplitView)"
else
    echo "❌ 侧边栏界面未实现"
fi

# 检查是否有侧边栏项目定义
if grep -q "SidebarItem" NtfyClip/Views/MainWindowView.swift; then
    echo "✅ 侧边栏项目已定义"
    echo "   - 状态页面: $(grep -q "status" NtfyClip/Views/MainWindowView.swift && echo "✅" || echo "❌")"
    echo "   - 设置页面: $(grep -q "settings" NtfyClip/Views/MainWindowView.swift && echo "✅" || echo "❌")"
    echo "   - 日志页面: $(grep -q "logs" NtfyClip/Views/MainWindowView.swift && echo "✅" || echo "❌")"
else
    echo "❌ 侧边栏项目未定义"
fi

echo ""
echo "📊 检查日志管理系统..."

if [ -f "NtfyClip/Services/LogManager.swift" ]; then
    echo "✅ 日志管理器已实现"
    echo "   - LogEntry 模型: $(grep -q "struct LogEntry" NtfyClip/Services/LogManager.swift && echo "✅" || echo "❌")"
    echo "   - LogLevel 枚举: $(grep -q "enum LogLevel" NtfyClip/Services/LogManager.swift && echo "✅" || echo "❌")"
    echo "   - LogManager 类: $(grep -q "class LogManager" NtfyClip/Services/LogManager.swift && echo "✅" || echo "❌")"
else
    echo "❌ 日志管理器未实现"
fi

echo ""
echo "🔄 检查循环保护机制..."

# 检查 Actor 类是否更新以支持双地址配置
if grep -q "enableSending" NtfyClip/Actors/ClipboardSender.swift; then
    echo "✅ ClipboardSender 已更新支持双地址配置"
else
    echo "❌ ClipboardSender 未更新"
fi

if grep -q "enableReceiving" NtfyClip/Actors/ClipboardReceiver.swift; then
    echo "✅ ClipboardReceiver 已更新支持双地址配置"
else
    echo "❌ ClipboardReceiver 未更新"
fi

echo ""
echo "🧪 功能测试建议..."
echo "=================================="
echo "1. 📱 打开主窗口，检查侧边栏是否显示三个页面"
echo "2. 🔧 进入设置页面，验证是否有发送和接收的独立配置"
echo "3. 📊 查看状态页面，确认显示连接状态和统计信息"
echo "4. 📝 检查日志页面，验证日志记录和过滤功能"
echo "5. 🔄 测试双地址配置的同步功能"

echo ""
echo "💡 配置测试建议："
echo "   发送地址: https://ntfy.sh"
echo "   发送主题: test-send-$(date +%s)"
echo "   接收地址: https://ntfy.sh"
echo "   接收主题: test-receive-$(date +%s)"

echo ""
echo "🎯 测试完成！"
echo "如果所有检查都显示 ✅，说明新功能已成功实现。"
echo "请手动测试用户界面以验证功能完整性。"
