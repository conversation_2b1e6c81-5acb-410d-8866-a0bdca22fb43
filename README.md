# NtfyClip 📋

**A beautiful, modern macOS clipboard synchronization app with Apple's Liquid Glass design system**

NtfyClip is a native macOS application that seamlessly synchronizes your clipboard across devices using the ntfy notification service. Built with SwiftUI and featuring Apple's cutting-edge Liquid Glass visual effects, it provides an elegant and intuitive user experience that feels right at home on macOS.

![macOS](https://img.shields.io/badge/macOS-13.0+-blue.svg)
![Swift](https://img.shields.io/badge/Swift-5.7+-orange.svg)
![SwiftUI](https://img.shields.io/badge/SwiftUI-4.0+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## ✨ Features

### 🎨 **Liquid Glass Design System**
- **Modern Visual Effects**: Implements Apple's latest Liquid Glass design language with sophisticated transparency, depth, and material effects
- **Adaptive Interface**: Beautiful glass cards, buttons, and backgrounds that respond to user interaction
- **Enhanced Sidebar**: Optimized navigation with improved spacing, touch targets, and vibrant visual feedback
- **Consistent Styling**: Unified design language throughout the entire application

### 🔄 **Advanced Clipboard Synchronization**
- **Bidirectional Sync**: Real-time two-way synchronization between clipboard and ntfy topics
- **Smart Loop Protection**: Intelligent algorithms prevent infinite sync loops
- **Content Type Support**: Handles plain text with robust encoding support
- **Separate Tracking**: Individual counters for sent and received items with detailed statistics

### 🏗️ **Modern Architecture**
- **Swift Actor Model**: Concurrent, thread-safe operations using modern Swift concurrency
- **Three-Layer Design**: Clean separation of Views, ViewModels, and Services
- **NavigationSplitView**: Native macOS sidebar navigation following Human Interface Guidelines
- **Real-time Updates**: Live status monitoring and instant feedback

### 🔐 **Enterprise-Ready Security**
- **Authentication Support**: Username/password authentication for private ntfy servers
- **Secure Configuration**: Encrypted storage of sensitive settings
- **Error Resilience**: Graceful error handling with automatic reconnection

### 📊 **Comprehensive Monitoring**
- **Live Dashboard**: Real-time sync activity with beautiful glass-styled counters
- **Detailed Statistics**: Separate tracking for sent/received notifications
- **Activity Logs**: Comprehensive logging system for debugging and monitoring
- **Connection Status**: Visual indicators for server connectivity and sync state

## 🖥️ System Requirements

- **macOS**: 13.0 (Ventura) or later
- **Xcode**: 15.0+ (for development)
- **Swift**: 5.9+ command-line tools
- **Memory**: 50MB RAM minimum
- **Storage**: 10MB disk space

## 🚀 Quick Start

### Option 1: Using Xcode (Recommended for Development)

```bash
# Clone the repository
git clone https://github.com/your-username/NtfyClip.git
cd NtfyClip

# Open in Xcode
open NtfyClip.xcodeproj

# Build and run (⌘+R)
```

### Option 2: Command Line Build

```bash
# Clone and navigate
git clone https://github.com/your-username/NtfyClip.git
cd NtfyClip

# Build the project
xcodebuild -project NtfyClip.xcodeproj -scheme NtfyClip -configuration Release build

# Run the application
open /path/to/built/NtfyClip.app
```

## 📱 User Interface Overview

### Main Window
The application features a modern three-panel interface with Liquid Glass styling:

- **Sidebar Navigation**: Enhanced with improved spacing and touch targets
  - Status Dashboard
  - Settings Configuration  
  - Activity Logs

- **Content Area**: Dynamic views with glass effects and smooth transitions
- **Toolbar**: Liquid Glass styled controls with subtle animations

### Menu Bar Integration
- Lightweight menu bar presence with quick access controls
- Start/stop sync functionality
- Direct access to main window and settings

## ⚙️ Configuration

### Basic Setup
1. **Launch NtfyClip** - The main window opens automatically
2. **Navigate to Settings** - Click the settings icon in the sidebar
3. **Configure Server Settings**:
   - **Server URL**: `https://ntfy.sh` (or your private server)
   - **Topic Name**: Choose a unique topic (e.g., `my-clipboard-sync`)
   - **Authentication**: Optional username/password for private servers

### Advanced Configuration
- **Dual Configuration**: Separate settings for send and receive operations
- **Authentication**: Support for private ntfy server credentials
- **Connection Testing**: Built-in connectivity verification

## 🏛️ Architecture

NtfyClip follows modern macOS app development best practices with a clean three-layer architecture:

### 🎨 View Layer (SwiftUI + Liquid Glass)
```
MainWindowView.swift          # Main app window with NavigationSplitView
├── StatusDetailView.swift    # Dashboard with glass-styled counters
├── SettingsView.swift        # Configuration interface with glass cards
├── MenuBarView.swift         # Menu bar integration
└── LiquidGlassComponents/    # Reusable glass effect components
    ├── LiquidGlassButton     # Glass-styled interactive buttons
    ├── LiquidGlassCard       # Container components with depth
    ├── LiquidGlassToolbar    # Navigation and header styling
    └── LiquidGlassBackground # Full-screen background effects
```

### 🧠 ViewModel Layer
```
AppViewModel.swift            # Central state management
├── Connection state tracking
├── Statistics management (sent/received counters)
├── Error handling and user feedback
└── Sync control and coordination
```

### ⚙️ Services & Actors Layer
```
Actors/
├── ClipboardSender.swift     # Outbound clipboard monitoring
└── ClipboardReceiver.swift   # Inbound ntfy message handling

Services/
├── NtfyService.swift         # HTTP communication with ntfy servers
├── ClipboardManager.swift    # System clipboard integration
├── ConfigurationManager.swift # Settings persistence and validation
└── LogManager.swift          # Comprehensive logging system

Models/
└── AppModels.swift           # Data structures and configuration models
```

## 🎯 Key Features Deep Dive

### Liquid Glass Visual System
- **Material Effects**: Ultra-thin, thin, and regular material backgrounds
- **Gradient Overlays**: Subtle color gradients for depth and warmth
- **Interactive States**: Smooth hover and selection animations
- **Consistent Spacing**: Optimized touch targets and visual hierarchy

### Clipboard Synchronization
- **Real-time Monitoring**: Instant detection of clipboard changes
- **Bidirectional Flow**: Send local clipboard to ntfy, receive remote updates
- **Loop Prevention**: Smart algorithms prevent infinite sync cycles
- **Content Validation**: Robust handling of different text encodings

### Statistics and Monitoring
- **Live Counters**: Real-time display of sent/received item counts
- **Session Tracking**: Statistics reset with each app launch
- **Visual Feedback**: Glass-styled progress indicators and status displays
- **Detailed Logging**: Comprehensive activity logs for troubleshooting

## 🔧 Development

### Building from Source
```bash
# Ensure you have Xcode 15.0+ installed
xcode-select --install

# Clone and build
git clone https://github.com/your-username/NtfyClip.git
cd NtfyClip
xcodebuild -project NtfyClip.xcodeproj -scheme NtfyClip build
```

### Project Structure
```
NtfyClip/
├── NtfyClipApp.swift         # App entry point and lifecycle
├── Views/                    # SwiftUI interface components
├── ViewModels/               # State management and business logic
├── Actors/                   # Concurrent clipboard operations
├── Services/                 # External integrations and utilities
├── Models/                   # Data structures and configurations
├── Utils/                    # Helper functions and extensions
└── Assets.xcassets/          # App icons and visual resources
```

## 🤝 Contributing

We welcome contributions! Please feel free to submit issues, feature requests, or pull requests.

### Development Guidelines
- Follow Swift and SwiftUI best practices
- Maintain the three-layer architecture
- Preserve Liquid Glass design consistency
- Include appropriate tests for new features

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Made with ❤️ for the macOS community**
