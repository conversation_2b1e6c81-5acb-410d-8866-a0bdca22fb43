#!/usr/bin/env python3
"""
Simple test for NtfyClip - just test clipboard functionality
"""

import subprocess
import time
from datetime import datetime

def log(message):
    """Log a message with timestamp"""
    print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

def check_clipboard():
    """Check current clipboard content"""
    try:
        result = subprocess.run(["pbpaste"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            content = result.stdout
            log(f"📋 Clipboard: '{content[:100]}{'...' if len(content) > 100 else ''}'")
            return content
        else:
            log("❌ Failed to read clipboard")
            return None
    except Exception as e:
        log(f"❌ Error reading clipboard: {e}")
        return None

def set_clipboard(content):
    """Set clipboard content"""
    try:
        result = subprocess.run(["pbcopy"], input=content, text=True, timeout=5)
        if result.returncode == 0:
            log(f"📝 Set clipboard to: '{content[:100]}{'...' if len(content) > 100 else ''}'")
            return True
        else:
            log("❌ Failed to set clipboard")
            return False
    except Exception as e:
        log(f"❌ Error setting clipboard: {e}")
        return False

def main():
    """Main test function"""
    log("🚀 Starting simple NtfyClip test")
    
    print("\n" + "="*60)
    print("This test will:")
    print("1. Check current clipboard content")
    print("2. Set test content to clipboard")
    print("3. Monitor for changes")
    print("4. Verify NtfyClip is detecting clipboard changes")
    print("\nMake sure NtfyClip is running!")
    print("="*60 + "\n")
    
    # Test 1: Check initial clipboard
    log("📋 Checking initial clipboard state...")
    initial_content = check_clipboard()
    
    # Test 2: Set test content
    test_content = f"NtfyClip test message - {datetime.now().strftime('%H:%M:%S')}"
    log("📝 Setting test content to clipboard...")
    if set_clipboard(test_content):
        log("⏳ Waiting 3 seconds for NtfyClip to detect change...")
        time.sleep(3)
        
        # Verify content is still there
        current_content = check_clipboard()
        if current_content == test_content:
            log("✅ Clipboard content verified")
        else:
            log("⚠️  Clipboard content changed (this might be normal if NtfyClip is working)")
    
    # Test 3: Set another test message
    test_content2 = f"Second test message - {datetime.now().strftime('%H:%M:%S')}"
    log("📝 Setting second test content...")
    if set_clipboard(test_content2):
        log("⏳ Waiting 3 seconds...")
        time.sleep(3)
        check_clipboard()
    
    log("🏁 Simple test completed!")
    log("💡 Check NtfyClip logs to see if it detected the clipboard changes")
    log("💡 If configured correctly, these messages should appear on your ntfy topic")

if __name__ == "__main__":
    main()
