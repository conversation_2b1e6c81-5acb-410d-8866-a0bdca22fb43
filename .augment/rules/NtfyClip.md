---
type: "always_apply"
---

项目名称：NtfyBoard for macOS

使用前调用 Context7 MCP 去阅读文档, 使用最新的技术, 最新的 UI 设计

项目要完全改写成 Liquid Glass 风格, 使用到如下网页, 在修改 swift ui 相关时, 先查询如下控件的 liquid galss 用法

https://developer.apple.com/documentation/technologyoverviews/adopting-liquid-glass
https://developer.apple.com/documentation/SwiftUI/View/glassEffect(_:in:isEnabled:)
https://developer.apple.com/documentation/SwiftUI/View/backgroundExtensionEffect()
https://developer.apple.com/documentation/SwiftUI/NavigationSplitView
https://developer.apple.com/documentation/SwiftUI/Button
https://developer.apple.com/documentation/SwiftUI/NavigationStack
https://developer.apple.com/documentation/SwiftUI/NavigationSplitView
https://developer.apple.com/documentation/SwiftUI/WindowStyle/titleBar
https://developer.apple.com/documentation/SwiftUI/View/toolbar(content:)
https://developer.apple.com/documentation/SwiftUI/Toggle
https://developer.apple.com/documentation/SwiftUI/Slider
https://developer.apple.com/documentation/SwiftUI/Picker
https://developer.apple.com/documentation/SwiftUI/TabViewStyle/sidebarAdaptable
https://developer.apple.com/design/human-interface-guidelines/toolbars


避免写死的按钮颜色, 以适配系统级别的深色 暗色模式
