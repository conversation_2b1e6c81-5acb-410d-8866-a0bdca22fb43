// swift-tools-version: 5.7
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "NtfyClip",
    platforms: [
        .macOS(.v13)
    ],
    products: [
        .executable(
            name: "NtfyClip",
            targets: ["Ntfy<PERSON>lip"]
        ),
    ],
    dependencies: [
        // Add any external dependencies here if needed
        // For example:
        // .package(url: "https://github.com/apple/swift-crypto.git", from: "2.0.0"),
    ],
    targets: [
        .executableTarget(
            name: "NtfyClip",
            dependencies: [
                // Add dependencies here
            ],
            path: "NtfyClip",
            resources: [
                .process("Info.plist"),
                .process("NtfyClip.entitlements")
            ]
        ),
        .testTarget(
            name: "NtfyClipTests",
            dependencies: ["NtfyClip"],
            path: "NtfyClipTests"
        ),
    ]
)
