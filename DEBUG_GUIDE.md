# NtfyClip Debugging and Testing Guide

## 🚀 Application Startup Checklist

### 1. Confirm the Application is Running
```bash
# Check for the process
ps aux | grep NtfyClip

# You should see output similar to this:
# username  12345   0.0  0.4  ... .build/Build/Products/Debug/NtfyClip.app/Contents/MacOS/NtfyClip
```

### 2. Check the Startup Logs
The application will output the following to the console when run from Xcode or the command line:
```
🚀 NtfyClip starting up...
📱 Look for the clipboard icon in your menu bar
🪟 A main window should also appear as backup
🔧 AppViewModel: Initializing...
✅ AppViewModel: Initialization complete
```

## 🪟 UI Checklist

### Main Window (should appear automatically)
- [ ] A window with the title "NtfyClip" should be visible.
- [ ] The window should have a sidebar with "Status", "Settings", and "Logs".

### Menu Bar Icon
- [ ] A clipboard icon 📋 should be visible in the menu bar.
- [ ] Clicking the icon should show a dropdown menu.

## 🔧 Troubleshooting Steps

### If you don't see the main window:
1.  **Check the Dock** for the NtfyClip icon.
2.  **Use the App Switcher** (⌘+Tab) to find NtfyClip.
3.  **Check Mission Control** (F3 or four-finger swipe up).

### If you don't see the menu bar icon:
1.  **Check System Settings > Control Center** to ensure menu bar icons are not hidden.
2.  **Restart the application**:
    ```bash
    pkill -f NtfyClip
    make run
    ```

## 🧪 Functional Test Plan

### Phase 1: Basic UI Testing
1.  [ ] Application starts successfully.
2.  [ ] Main window displays correctly.
3.  [ ] Menu bar icon is visible.
4.  [ ] Sidebar navigation works.

### Phase 2: Configuration Testing
1.  [ ] Open the Settings tab.
2.  [ ] Enter a ntfy server URL: `https://ntfy.sh`
3.  [ ] Enter a topic name: `test-clipboard-[random-number]`
4.  [ ] Save the configuration.
5.  [ ] Verify that the configuration is valid (green checkmark).

### Phase 3: Sync Functionality Testing
1.  [ ] Click "Start Sync" in the Status tab.
2.  [ ] The status should change to "Connected" (green).
3.  [ ] Copy text to the clipboard.
4.  [ ] Check the ntfy web interface to see if the message was received.
5.  [ ] Send a message from the ntfy web interface.
6.  [ ] Check if the local clipboard is updated.

## 🐛 Common Issues and Solutions

### Issue 1: Application starts but no UI is visible
**Symptom**: The process is running, but you can't see the window or menu bar icon.
**Solution**:
```bash
# Rebuild and run the application
make clean
make build
make run
```

### Issue 2: Invalid Configuration
**Symptom**: The Settings tab shows a red X for the configuration.
**Solution**:
-   Ensure the URL starts with `http://` or `https://`.
-   Ensure the topic name is not empty.

### Issue 3: Syncing is not working
**Symptom**: The status is "Connected", but content is not syncing.
**Solution**:
-   Verify the topic name is correct.
-   Check the Logs tab for any error messages.

## 🔍 Logging and Debugging

### View System Logs
```bash
# View application logs
log show --predicate 'subsystem == "com.ntfyclip.app"' --last 1h
```

### Console App
1.  Open the Console.app.
2.  Search for "com.ntfyclip.app".
3.  View the real-time logs.

### Debug Output
The application will print debug information to the terminal, including:
- 🚀 Startup information
- 🔧 Initialization status
- ✅ Completion status
- ❌ Error messages
