#!/bin/bash

echo "🧪 NtfyClip 应用程序测试脚本"
echo "================================"

# 检查应用程序是否正在运行
echo "📱 检查应用程序状态..."
if pgrep -f "NtfyClip.app" > /dev/null; then
    echo "✅ NtfyClip 应用程序正在运行"
    
    # 显示进程信息
    echo "📊 进程信息:"
    ps aux | grep -v grep | grep NtfyClip | head -5
    
    echo ""
    echo "🔍 应用程序位置:"
    find /Users/<USER>/Library/Developer/Xcode/DerivedData -name "NtfyClip.app" -type d 2>/dev/null | head -1
    
    echo ""
    echo "📋 检查菜单栏图标..."
    echo "请查看您的菜单栏右侧是否有剪贴板图标 📋"
    
    echo ""
    echo "🪟 检查主窗口..."
    echo "请查看是否有 NtfyClip 主窗口显示"
    
    echo ""
    echo "⚙️ 下一步操作:"
    echo "1. 查看菜单栏右侧的剪贴板图标"
    echo "2. 点击图标打开菜单"
    echo "3. 如果看到主窗口，点击 'Open Settings' 配置应用"
    echo "4. 输入 ntfy 服务器 URL 和主题名称"
    echo "5. 点击 'Start Sync' 开始同步"
    
else
    echo "❌ NtfyClip 应用程序未运行"
    echo "尝试启动应用程序..."
    
    # 查找应用程序
    APP_PATH=$(find /Users/<USER>/Library/Developer/Xcode/DerivedData -name "NtfyClip.app" -type d 2>/dev/null | head -1)
    
    if [ -n "$APP_PATH" ]; then
        echo "🚀 启动应用程序: $APP_PATH"
        open "$APP_PATH"
        sleep 3
        
        if pgrep -f "NtfyClip.app" > /dev/null; then
            echo "✅ 应用程序启动成功"
        else
            echo "❌ 应用程序启动失败"
        fi
    else
        echo "❌ 找不到 NtfyClip.app"
        echo "请先构建项目: xcodebuild -project NtfyClip.xcodeproj -scheme NtfyClip build"
    fi
fi

echo ""
echo "🔧 故障排除:"
echo "如果看不到界面，请尝试:"
echo "1. 检查 Dock 中是否有 NtfyClip 图标"
echo "2. 使用 ⌘+Tab 切换到 NtfyClip"
echo "3. 检查菜单栏右侧（可能需要展开隐藏的图标）"
echo "4. 重启应用程序"

echo ""
echo "📞 获取帮助:"
echo "查看 DEBUG_GUIDE.md 获取详细的调试信息"
